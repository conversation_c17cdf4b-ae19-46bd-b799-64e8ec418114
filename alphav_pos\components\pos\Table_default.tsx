import { Table, Box, HStack,Button,Flex,Stack } from '@chakra-ui/react';
import { PaginationItems, PaginationNextTrigger, PaginationPageText, PaginationPrevTrigger, PaginationRoot, } from "@/components/ui/pagination"
const DynamicTableWithPagination = ({ columns, columnSizes, data, height, width, currentPage, totalPages, handlePageChange, pageSize,getActions  }) => {
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedData = data.slice(startIndex, startIndex + pageSize);
  return (
    <Stack direction='column'  h={height}  w={width} mr={2}>
   
      <Table.Root variant="outline" stickyHeader size={'sm'} striped>
        <Table.Header>
          <Table.Row bg="bg.subtle">
            {columns.map((col, index) => (
              <Table.ColumnHeader key={index} textAlign={col.align || 'left'}>
                {col.label}
              </Table.ColumnHeader>
            ))}
          </Table.Row>
        </Table.Header>

        <Table.Body>
        {paginatedData.map((item) => (
            <Table.Row key={item.id}>
              {columns.map((col, colIndex) => (
                <Table.Cell key={colIndex} textAlign={col.align || 'left'}>
                  {col.key === 'actions' ? (
                    <HStack gap={2} justify="center">
                      {getActions(item).map((action, actionIndex) => (
                        <Button
                          key={actionIndex}
                          size="xs"
                          colorScheme={action.colorScheme}
                          onClick={action.onClick}
                        >{action.label}</Button>
                      ))}
                    </HStack>
                  ) : (item[col.key])}
                </Table.Cell>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table.Root>

      {/* Pagination Controls */}
      <PaginationRoot onPageChange={handlePageChange} page={currentPage} count={totalPages} pageSize={pageSize} defaultPage={1} size='sm' >
        <HStack justify="space-between" align="center" >
          <PaginationPageText />
          <Flex justify="center" align="center" gap={2}>
            <PaginationPrevTrigger>Previous</PaginationPrevTrigger>
            <PaginationItems />
            <PaginationNextTrigger>Next</PaginationNextTrigger>
          </Flex>
        </HStack>
      </PaginationRoot>
    </Stack>
  );
};

export default DynamicTableWithPagination;
