'use client'

import { ReactElement,ReactNode  } from 'react'
import { Box, SimpleGrid, Heading,HStack,VStack, Text, Image,Flex,Card,useBreakpointValue } from '@chakra-ui/react'
import {featurespage4,caveat,MotionBox,MotionSimpleGrid,MotionCard,MotionImage} from '@/utils/data'
// import { motion } from "motion/react"
// import {Arrow} from '@/components/custom/page2'

interface FeatureProps {
  title: string
  icon: String 
  children?: ReactNode
}

const variants ={
  hidden:{opacity:0},
  show:{
    opacity:1,
    transition:{
      staggerChildren:0.3,
    }

  }

}
const images ={
  hidden:{opacity:0,x:30},
  show:{opacity:1,x:0,transition:{duration:1,}}
}

const Feature = ({ title, icon, children }: FeatureProps ) => {
  const cardWidth = useBreakpointValue({ base: "100%", md: "100%", lg: "100%" });
  return (
    <HStack 
    gap={4} 
    justify="center" 
    wrap="wrap" 
    align="start"
    
  >
    <MotionCard
      variants={variants}
      initial="hidden"
      animate="show"
      overflow="hidden"
      shadow="xl"
      width={cardWidth}
      height={400}
    >
      <Card.Body _hover={{shadow: "sm"}} >
      <Text fontWeight={600} fontSize={{ base: 'md', md: 'lg', lg: 'xl' }} >
          {title}
        </Text>
 
        <MotionImage 
          src={icon}
          alt={title} 
          variants={images}
          fill={true}
          sizes="(max-width: 450px) 100vw, 450px" 
          style={{ objectFit: 'contain', marginTop:'36px',padding:'20px'}}
        />

        {children && <Box mt={2}>{children}</Box>}
      </Card.Body>
    </MotionCard>
  </HStack>


  )
}

export default function page3() {

  const isLargeScreen = useBreakpointValue({ base: false, md: true });
  return (
      <Flex  pt={{base:'0',md:'70px'}}>
        <VStack>
        <Heading  textAlign={'center'}  size="6xl" className={caveat.className} >Services and Packages</Heading>
          <MotionBox gap={{ base: 3, md: 5 }} pl={7}  pr={7}>
                  <MotionSimpleGrid columns={{ base: 1, md: 4 }} gap={{ base: 3, md: 5 }}>
                  {featurespage4.map((feature, index) => (
                  <Feature key={index}  icon={feature.icon}title={feature.title}  children/>
                    ))}
                  </MotionSimpleGrid>
                </MotionBox>
          </VStack>
      </Flex>

  )
}

