// export const formatDate = (dateString) => {
//     if (!dateString) return 'N/A';
//     return new Intl.DateTimeFormat('en-US', {
//       timeZone: 'Africa/Nairobi',
//       year: 'numeric', 
//       month: '2-digit', 
//       day: '2-digit', 
//       hour: '2-digit', 
//       minute: '2-digit', 
//       second: '2-digit',
//     }).format(new Date(dateString));
//   };
  

export const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Intl.DateTimeFormat('en-US', {
      timeZone: 'Africa/Nairobi',
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit'
    }).format(new Date(dateString));
  };
  

  export const formatCurrency = (amount: number | string | null) => {
    if (amount === null || amount === undefined) return 'KSH 0.00';
    const value = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `KSH ${value.toFixed(2)}`;
  };