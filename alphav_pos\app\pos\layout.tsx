"use client";
import { Box, Flex, IconButton, useDisclosure } from "@chakra-ui/react";
import { FiMenu } from "react-icons/fi";
import { Toaster } from "@/components/ui/toaster";
import Sidebar, { Menu_shops } from '@/components/pos/sidebar'
import { useState, useRef } from "react";
import RouteGuard from "@/components/auth/RouteGuard";

export default function Layout({ children }: { children: React.ReactNode }) {
  const drawerRef = useRef<HTMLDivElement | null>(null);
  // const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(true);
  const openDrawer = () => {
    if (drawerRef.current) {
      drawerRef.current.style.display = "block";
      // setIsDrawerOpen(true);
    }
  };

  const closeDrawer = () => {
    if (drawerRef.current) {
      drawerRef.current.style.display = "none";
      // setIsDrawerOpen(false);
    }
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <RouteGuard>
      <Box position="relative">
        <Toaster />
        <Flex position="relative" minH="100vh">
          <IconButton
            aria-label="Menu"
            position="fixed"
            top="4"
            left="4"
            bg="black"
            _dark={{ bg: "white" }}
            p={2}
            zIndex="2"
            display={{ base: "block", md: "none" }}
            onClick={toggleCollapse}
          ><FiMenu /></IconButton>

          <Box
            as="aside"
            position={{ base: "fixed", md: "sticky" }}
            top="0"
            left="0"
            height="100vh"
            zIndex={{ base: "overlay", md: "sticky" }}
            transition="all 0.3s ease"
            transform={{
              base: isCollapsed ? "translateX(-100%)" : "translateX(0)",
              md: "none"
            }}
            width={{
              base: "240px",
              md: isCollapsed ? "4rem" : "11.5rem"
            }}
          >
            <Sidebar isCollapsed={isCollapsed} toggleCollapse={toggleCollapse} />
          </Box>

          <Box
            as="main"
            flex="1"
            minH="100vh"
            transition="all 0.3s ease"
            pt={{ base: "16", md: "4" }}
            width="full"
          >
            {children}
          </Box>
        </Flex>
      </Box>
    </RouteGuard>
  );
}