import React,{useState,useEffect,useMemo,useRef,useCallback} from 'react'
import {Text,Stack,Box,Center,Card,VStack,HStack,Input,Table,Circle,Float ,Heading,Flex,Separator,Button,Skeleton,ProgressCircle} from '@chakra-ui/react';


const Page = ({totals}) => {
    return (
      <Box>
              <Box borderRadius="lg" boxShadow="xl" p={4} position="relative">
                {totals
                    .filter((item) => item.name != "Total_Items") 
                    .map((item) => (
                      <HStack key={item.name} justify="space-between" w="25rem">
                        <Heading textStyle="md" textAlign="start">{item.name}:</Heading>
                        <Text textAlign="end">{item.amount}</Text>
                      </HStack>
                    ))}
              </Box>
      </Box>
    );
  };

  export default Page;