"use client";
import { useRef, useEffect } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { makeStore } from "@/lib/store";
import { setGlobalStore } from "@/app/utils/pos";

export default function StoreProvider({ children }: { children: React.ReactNode }) {

  const storeRef = useRef<ReturnType<typeof makeStore> | null>(null);

  if (!storeRef.current) {storeRef.current = makeStore();}

  const { store, persistor } = storeRef.current;

  // Set the global store reference for API utilities
  useEffect(() => {
    setGlobalStore(store);
  }, [store]);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
}