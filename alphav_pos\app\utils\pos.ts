import axios from 'axios';
import { ApiResponse } from '@/app/utils/definitions';

// const baseApiUrl = process.env.NEXT_PUBLIC_BASE_API_URL;
export const baseApiUrl = 'https://pos-backend-five.vercel.app/api';
// export const baseApiUrl = 'http://localhost:5001/api';

// Global store reference for accessing Redux state outside React components
let globalStore: any = null;

// Function to set the store reference (called from StoreProvider)
export const setGlobalStore = (store: any) => {
  globalStore = store;
};

// Helper function to get current user info from Redux store
const getCurrentUserInfo = () => {
  if (!globalStore) {
    // Fallback: try to get from localStorage if store not available
    try {
      const persistedState = localStorage.getItem('persist:root');
      if (persistedState) {
        const parsed = JSON.parse(persistedState);
        const userState = JSON.parse(parsed.user);
        const currentUser = userState.currentUser;

        if (currentUser?.authenticated) {
          return {
            accesskey: currentUser.accesskey,
            shop_id: currentUser.currentshop,
            user_id: currentUser.user_id,
          };
        }
      }
    } catch (error) {
      console.warn('Could not access user info from localStorage:', error);
    }
    return null;
  }

  const state = globalStore.getState();
  const currentUser = state.user.currentUser;

  if (!currentUser?.authenticated) {
    return null;
  }

  return {
    accesskey: currentUser.accesskey,
    shop_id: currentUser.currentshop,
    user_id: currentUser.user_id,
  };
};

// Helper function to add auth parameters to requests
const addAuthParams = (params: any = {}) => {
  const userInfo = getCurrentUserInfo();
  if (userInfo) {
    return {
      ...params,
      accesskey: userInfo.accesskey,
      shop_id: userInfo.shop_id,
    };
  }
  return params;
};

// Helper function to add auth headers
const getAuthHeaders = () => {
  const userInfo = getCurrentUserInfo();
  if (userInfo) {
    return {
      'X-Access-Key': userInfo.accesskey,
      'X-Shop-ID': userInfo.shop_id,
      'X-User-ID': userInfo.user_id,
    };
  }
  return {};
};

// Export utility functions for manual use
export const getAuthInfo = getCurrentUserInfo;
export const getAuthParams = addAuthParams;
export const getAuthHeadersExport = getAuthHeaders;

// Fetch data (GET)
export const fetchData = async <T>(
  endpoint: string,
  page: number,
  pageSize: number,
  query?: any,
): Promise<ApiResponse<T>> => {
  try {
    const params = { page, pageSize, query };

    // Automatically add auth parameters
    const authParams = addAuthParams(params);
    const authHeaders = getAuthHeaders();

    const response = await axios.get<ApiResponse<T>>(`${baseApiUrl}/${endpoint}`, {
      params: authParams,
      headers: authHeaders,
      withCredentials: true,
    });
    return response.data;
  } catch (error) {
    console.error('API Error (GET):', error);
    throw new Error('Failed to fetch data.', error);
  }
};

export const fetchData2 = async <T>(
  endpoint: string,
  page?: number,
  pageSize?: number,
  query?: any,
  id?: number | string
): Promise<ApiResponse<T>> => {
  try {
    const params: Record<string, any> = {};
    if (page !== undefined) params.page = page;
    if (pageSize !== undefined) params.pageSize = pageSize;
    if (query && typeof query == "object" && !Array.isArray(query)) {
      Object.keys(query).forEach((key) => {
        params[key] = query[key];
      });
    } else if (typeof query == "string") {
      params.query = query;
    }

    // Automatically add auth parameters
    const authParams = addAuthParams(params);
    const authHeaders = getAuthHeaders();

    const url = id ? `${baseApiUrl}/${endpoint}/${id}` : `${baseApiUrl}/${endpoint}`;

    const response = await axios.get<ApiResponse<T>>(url, {
      params: authParams,
      headers: authHeaders,
      withCredentials: true,
    });
    return response.data;
  } catch (error) {
    console.error('API Error (GET):', error.response);
    throw new Error(error.response.data.error ? error.response.data.error :'Failed to fetch data.');
  }
};


export const fetchImage = async (filename: string): Promise<string | null> => {
  try {
    // Automatically add auth headers
    const authHeaders = getAuthHeaders();

    const response = await axios.get(`${baseApiUrl}/pic/${filename}`, {
      responseType: 'blob',
      headers: authHeaders,
      withCredentials: true,
    });

    if (response.status === 200) {
      const imageUrl = URL.createObjectURL(response.data);
      return imageUrl;
    } else {
      // throw new Error('Failed to fetch data: ' + response.statusText);
      return null;
    }
  } catch (error) {
    return null;
    // console.error('Error fetching image:', error);
    // throw new Error(error.response?.data?.error || 'Failed to fetch data.');
  }
}

// Create data (POST)
export const insertData = async <T>(
  endpoint: string,
  data: T
): Promise<T> => {
  try {
    const noCredentialsEndpoints = [
      'auth_login',
      'auth_forgot',
    ];
    const requiresCredentials = !noCredentialsEndpoints.includes(endpoint);

    // Automatically add auth headers for authenticated endpoints
    const authHeaders = requiresCredentials ? getAuthHeaders() : {};

    const config = {
      withCredentials: requiresCredentials,
      headers: authHeaders,
    };

    const response = await axios.post<T>(`${baseApiUrl}/${endpoint}`, data, config);

    return response.data;
  } catch (error) {
    console.log(error.response?.data?.error)
    const errorMessage = error.response?.data?.error || 'Failed to fetch data.';
    throw new Error(errorMessage);
  }
};


// Create data (POST)
export const insertBulk = async <T>(
  endpoint: string,
  data: T | FormData
): Promise<T> => {
  try {
    const isFormData = data instanceof FormData;

    // Automatically add auth headers
    const authHeaders = getAuthHeaders();

    const config = isFormData
      ? {
          headers: {
            'Content-Type': 'multipart/form-data',
            ...authHeaders
          },
          withCredentials: true,
        }
      : {
          headers: {
            'Content-Type': 'application/json',
            ...authHeaders
          },
          withCredentials: true,
        };

    const response = await axios.post<T>(`${baseApiUrl}/${endpoint}`, data, config);
    return response.data;
  } catch (error) {
    console.error('API Error (POST):', error);
    throw new Error('Failed to create data.');
  }
};


// Update data (PATCH)
export const updateData = async <T>(
  endpoint: string,
  id: string | number,
  data: Partial<T>
): Promise<T> => {
  try {
    // Automatically add auth headers
    const authHeaders = getAuthHeaders();

    const response = await axios.put<T>(`${baseApiUrl}/${endpoint}/${id}`, data, {
      headers: authHeaders,
      withCredentials: true,
    });
    return response.data;
  } catch (error) {
    console.error('API Error (PATCH):', error.response.data.error);
    throw new Error(error.response.data.error?error.response.data.error:'Failed to update data.');
  }
};

// Delete data (DELETE)
export const deleteData = async (
  endpoint: string,
  id: string | number
): Promise<void> => {
  try {
    // Automatically add auth headers
    const authHeaders = getAuthHeaders();

    await axios.delete(`${baseApiUrl}/${endpoint}/${id}`, {
      headers: authHeaders,
      withCredentials: true,
    });
  } catch (error) {
    console.error('API Error (DELETE):', error);
    throw new Error('Failed to delete data.');
  }
};



export const deleteData2 = async (
  endpoint: string,
  payload: string | number | object
): Promise<void> => {
  try {
    // Automatically add auth headers
    const authHeaders = getAuthHeaders();

    if (typeof payload === 'string' || typeof payload === 'number') {
      await axios.delete(`${baseApiUrl}/${endpoint}/${payload}`, {
        headers: authHeaders,
        withCredentials: true
      });
    }
    else {
      await axios.post(`${baseApiUrl}/${endpoint}`, payload, {
        headers: authHeaders,
        withCredentials: true,
      });
    }
  } catch (error) {
    const errorMessage = error.response?.data?.message || 'Failed to delete data';
    console.error('API Error (DELETE):', errorMessage);
    throw new Error(errorMessage);
  }
};



// export const deleteData = async (
//   endpoint: string,
//   id: string | number
// ): Promise<void> => {
//   try {
//     await axios.delete(`${baseApiUrl}/${endpoint}/${id}`,{withCredentials: true,});
//   } catch (error) {
//     console.error('API Error (DELETE):', error);
//     throw new Error('Failed to delete data.');
//   }
// };
