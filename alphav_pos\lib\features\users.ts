import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { UserRole } from "@/app/utils/rbac";

export interface User {
  user_id: string;
  username: string;
  email: string;
  roles: UserRole[] | string[];
  shops: string[];
  authenticated: boolean;
  transaction_id: string;
  currentshop: string;
  accesskey: string;
}

interface UserState {
  currentUser: User | null;
}

const initialState: UserState = {
  currentUser: null,
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.currentUser) {
        state.currentUser = { ...state.currentUser, ...action.payload };
      }
    },
    logout: (state) => {
      state.currentUser = null;
    },
  },
});

export const { setUser, updateUser, logout } = userSlice.actions;
export default userSlice.reducer;