"use client"
import React,{useState,useEffect,useMemo,useRef,useCallback} from 'react'
import {Text,Stack,Box,Center,Card,HStack,Input,Table,Circle,Float ,Heading,Flex,Separator,Button,Skeleton,ProgressCircle, StackProps} from '@chakra-ui/react';
import { InputGroup } from "@/components/ui/input-group"
import { FiSearch, FiX} from 'react-icons/fi'
import Sale_T from '@/components/pos/sale_tab'
import { toaster } from "@/components/ui/toaster"
import { Shop,Product,Transaction,Order,Receipt,Category,Discount,ApiResponse,Status } from '@/app/utils/definitions'
import {insertBulk,fetchData,fetchData2,insertData,updateData,deleteData,fetchImage } from '@/app/utils/pos';
import { z } from 'zod';
import { useAppDispatch, useAppSelector } from "@/lib/hooks";
import { updateUser } from "@/lib/features/users";
import Receiptpage from '@/components/pos/receipt';

const receiptSchema = z.object({
  trans_id: z.string().min(1, "Transaction not initlaized"),
  receipt_item_id: z.string().optional(),
  receipt_item: z.string().min(1, "Item name is required"),
  receipt_each: z.number(),
  receipt_quantity: z.number().min(0.1, "Quantity must be at least 0.1"),
  receipt_total: z.number(),
  receipt_tax: z.number().min(0),
  receipt_net: z.number().min(0),
  shop_id: z.string(),
});

const Page = () => {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;
  const username = user?.username;

  const [query, setQuery] = useState('');
  const dispatch = useAppDispatch();
  const [latestReceipt, setLatestReceipt] = useState<Receipt | null>(null);
  const handleSearch = (searchValue: string) => {setQuery(searchValue);};
  const [transaction_id, setTransaction_id] = useState(user?.transaction_id);
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [Ismounted,setIsmounted] =useState(false)

  const initializeTransaction = useCallback(async () => {
        if(transaction_id == ''){
            try{
                if(user){
                    if (user?.transaction_id) {
                      setTransaction_id(user.transaction_id);
                    } else {
                      const newTransaction = await insertData<Transaction>("transaction", {shop_id: shop_id,trans_status: 0,username:user?.username});
                      setTransaction_id(newTransaction.trans_id);
                      dispatch(updateUser({ ...user, transaction_id: newTransaction.trans_id }));
                    }
                  } 
              }catch(error){
                toaster.create({title: 'Error with transaction logout/login',description: error, type: 'error',});
              }
          }
  },[transaction_id]);



  const checkTransaction = useCallback(async (type:any=null,trans:any=null) => {
    const currentTransactionId = transaction_id || trans;
  
    try {
      let fetchedTransaction: any | null = null;
      const response = await fetchData2<Transaction>("transaction",null,null,{username:username},currentTransactionId?currentTransactionId:null);
      if (response) {fetchedTransaction = response;}
      if(!type){
        if (!fetchedTransaction) {
              const newTransaction = await insertData<Transaction>("transaction",{shop_id: shop_id,trans_status: 0,username: user?.username,});
              if (newTransaction?.trans_id) {  
                await setTransaction_id(newTransaction?.trans_id);
                await dispatch(updateUser({ ...user, transaction_id: newTransaction?.trans_id }));
                await setTransaction(newTransaction?.data)
              }
        }
        if (fetchedTransaction?.shop_id != shop_id) {
            const newTransaction = await insertData<Transaction>("transaction", {shop_id,trans_status: 0,username: user?.username});
            if (newTransaction?.trans_id) {
              setTransaction_id(newTransaction.trans_id);
              dispatch(updateUser({ ...user, transaction_id: newTransaction.trans_id }));
              setTransaction(newTransaction);
              return;
            }
        }
    }  
    setTransaction_id(fetchedTransaction?.trans_id ? fetchedTransaction.trans_id:trans);
    dispatch(updateUser({ ...user, transaction_id: fetchedTransaction?.trans_id ? fetchedTransaction.trans_id:trans }));
    setTransaction(fetchedTransaction);
    } catch (error) {
      toaster.create({
        title: 'Failed to get Transaction.',
        description:'An unexpected error occurred.',
        type: 'error',
      });
    }finally{
      await setIsmounted(true)
    }
  }, [transaction_id, shop_id, user, dispatch]);




const handleBuy = useCallback(async (item, quantity) => {
    try {
      const price = quantity * item.item_selling;
      const newReceipt = {
        trans_id: transaction_id,
        receipt_item_id: item.item_id,
        receipt_item: item.item_name,
        receipt_each: Number(item.item_selling),
        receipt_quantity: quantity,
        receipt_total: price,
        receipt_tax: 0,
        receipt_net: 0,
        shop_id: shop_id,
      };
      const validatedReceipt = receiptSchema.parse(newReceipt);
      var x = await insertData<Receipt>('receipt', validatedReceipt);
      setLatestReceipt(x);
    } catch (error) {
      toaster.create({
        title: 'Failed to update receipt.',
        description:
          error instanceof z.ZodError
            ? error.errors[0]?.message + " " + error.errors[0]?.path
            : 'An unexpected error occurred.',
        type: 'error',
      });
    }
}, [transaction_id])


useEffect(() => {
  if (!transaction_id)initializeTransaction();
  // if(!Ismounted)checkTransaction();

}, [transaction_id, initializeTransaction]);
  
  return (
    <Box h="100vh"  ml={"3.7rem"}>
      <Stack  direction={{ base: "column", md: "row" }}>
          <Box>
          <Box display="flex" flexDirection="column" gap="4" w="100%" alignItems="stretch">
            <HStack 
              gap={2} 
              px={4}
              w="100%"
              justify="space-between"
              align="center"
            >
              <InputGroup 
                flex="1" 
                startElement={<FiSearch />} 
                w="100%"
              >
                <Input
                  placeholder="Search for Item"
                  size="md"
                  w="100%"
                  autoFocus
                  tabIndex={0}
                  cursor="text"
                  borderRadius="md"
                  transition="all 0.2s ease-in-out"
                  _hover={{ borderColor: "blue.400" }}
                  _focus={{ borderColor: "blue.500", boxShadow: "0 0 6px blue.400" }}
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </InputGroup>
              <Button
                colorScheme="blue"
                size="md"
                onClick={() => handleSearch(query)}
                transition="all 0.2s ease-in-out"
                _hover={{ transform: "scale(1.05)" }}
                _active={{ transform: "scale(0.98)" }}
              >
                Search
              </Button>
            </HStack>
            <Box flex="1" w="100%">
              <Receiptpage 
                handlesetTrans={setTransaction_id}  
                initializeTransaction={initializeTransaction}  
                transaction_id={transaction_id} 
                latestReceipt={latestReceipt} 
                shop_id={shop_id} 
                transaction={transaction} 
                checkTransaction={checkTransaction} 
              />
            </Box>
          </Box>
        </Box>
        <Box h="100vh">
          <Sale_T  
            query={query} 
            onBuy={handleBuy} 
            user={user} 
            setQuery={setQuery} 
            checkTransaction={checkTransaction}
          />
        </Box>
      </Stack>
      </Box>
              
  
  );
};


export default Page;
