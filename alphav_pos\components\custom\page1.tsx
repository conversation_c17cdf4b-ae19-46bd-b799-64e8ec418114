'use client'
import { useColorModeValue } from "@/components/ui/color-mode"
import {Box,Heading,Container,Text,Button,Stack,Icon,Flex,HStack,VStack} from '@chakra-ui/react'
import {header_note,phonex,phonex2,caveat} from "@/utils/data"


const getDailyImage = () => {
  const day = new Date().getDay();
  const images = ['1.jpg', '2.jpg', '3.jpg', '4.jpg', '5.jpg', '6.jpg', '7.jpg', '8.jpg'];
  return images[day % images.length];
};

export default function page1() {
  const dailyImage = getDailyImage();
  
  const scrollToElement = () => {
    const element = document.getElementById('1');
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };
  return (
<Box
  minHeight="100vh"
  // backgroundImage="url('/hero2.jpg')"
  backgroundImage={{
    base: `url('/hero/${dailyImage}')`,
    md: "url('/hero2.jpg')", 
  }}
  backgroundPosition={{ base: 'top', md: 'center' }}
  backgroundRepeat="no-repeat"
  backgroundSize={{ base: 'contain', md: 'cover' }}
>

  <Container zIndex={1}>


  <Stack
    as={Box}
    textAlign={'center'}
    gap={{ base: 8, md: 14 }}
    py={{ base: 20, md: 36 }}
  >
            <Heading
              fontWeight={600}
              fontSize={{ base: '4xl', sm: '4xl', md: '6xl' }}
              lineHeight={'110%'}
              color="blue.500"
            >Alpha's<br />
              <Text as={'span'} color="pink.600"  textStyle="7xl">
                Little Ones, Big Style!
              </Text>
            </Heading>
            <Text fontWeight={600} color="pink.600">{header_note}</Text>
            <HStack direction={'row'} gap={3} align={'center'} alignSelf={'center'}>
              <Button
                colorScheme={'pink'}
                bg="pink.600"
                rounded={'full'}
                px={6}
                _hover={{ bg: 'pink.600' }}
                onClick={scrollToElement}
              >
                Get Started
              </Button>
            </HStack>
  </Stack>
</Container>
</Box>
  )
}