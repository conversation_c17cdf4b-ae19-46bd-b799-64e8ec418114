'use client'

import { ReactElement,ReactNode  } from 'react'
import { Box, SimpleGrid, Heading,HStack,VStack, Text, Image,Flex,Card,useBreakpointValue } from '@chakra-ui/react'
// import { Slider } from "@/components/ui/slider"
import { FcAssistant, Fc<PERSON><PERSON>te, FcInTransit } from 'react-icons/fc'
import {featurespage3,caveat,MotionImage,MotionCard} from '@/utils/data'
import { motion } from "motion/react"
import PageWrapper from '@/components/custom/page-wrapper'
import Carousel from 'react-elastic-carousel'


interface FeatureProps {
  title: string
  text: string
  icon: String 
  children?: ReactNode
}




const variants ={
  hidden:{opacity:0},
  show:{
    opacity:1,
    transition:{
      staggerChildren:0.3,
    }

  }

}

const images ={
  hidden:{opacity:0,x:30},
  show:{opacity:1,x:0,transition:{duration:1,}}
}


// const MotionBox = motion.create(Box);
// const MotionSimpleGrid = motion.create(SimpleGrid);
// const MotionCard = motion.create(Card.Root);
// const MotionImage = motion.create(Image);

const Feature = ({ title, text, icon, children }: FeatureProps ) => {
  const cardWidth = useBreakpointValue({ base: "100%", md: "100%", lg: "100%" });
  return (
    <HStack 
    gap={4} 
    justify="center" 
    wrap="wrap" 
    align="start"
    // mb={8} 
  >
    <MotionCard
      variants={variants}
      initial="hidden"
      animate="show"
      overflow="hidden"
      shadow="xl"
      // width={cardWidth}
      height={{ base: "450px", md: "450px" }}
    >
      <Card.Body gap="2">
        <Box position="relative" width="100%" height="100%" rounded="md" overflow="hidden">
            <MotionImage 
              src={icon} 
              alt={title}   
              variants={images}
              fill
              sizes="(max-width: 450px) 100vw, 450px" 
              style={{ objectFit: 'cover',height:'100%',width:'100%'}}
            />
         </Box>
        <Flex mt={2}>
        </Flex>
        <Text fontWeight={600} fontSize={{ base: 'md', md: 'lg', lg: 'xl' }}>
          {title}
        </Text>
        <Text color="gray.600" fontSize={{ base: 'sm', md: 'md' }} isTruncated noOfLines={1}>
          {text}
        </Text>
        {/* {children && <Box mt={2}>{children}</Box>} */}
      </Card.Body>
    </MotionCard>
  </HStack>


  )
}

export default function page3() {

  const isLargeScreen = useBreakpointValue({ base: false, md: true });
  return (
    <Flex> 
        <VStack   pt={{base:'0',md:'70px'}}>
          <Heading  textAlign={'center'}  size="6xl" className={caveat.className} >Our Customers</Heading>
          <Box>
          {isLargeScreen ?
                  <Carousel focusOnSelect={true} itemsToShow={3} itemsToScroll={3} 
                  easing="cubic-bezier(1,.15,.55,1.54)"
                  tiltEasing="cubic-bezier(0.110, 1, 1.000, 0.210)"
                  transitionMs={700}
                  itemPadding={[10, 10]}
                  style={{ width: '100vw',paddingLeft:'30px',paddingRight:'30px' }}>
                              {featurespage3.map((feature, index) => (
                                <Feature
                                  key={index}
                                  icon={feature.icon}
                                  title={feature.title}
                                  text={feature.text}
                                />
                              ))}
                  </Carousel>:<Carousel focusOnSelect={true} itemsToShow={1} itemsToScroll={1}  enableAutoPlay 
                                    easing="cubic-bezier(1,.15,.55,1.54)"
                                    tiltEasing="cubic-bezier(0.110, 1, 1.000, 0.210)"
                                    transitionMs={700}
                                    showArrows={false}
                                    className="carousel-container"
                                    style={{ width: '100vw' }}>
                                                {featurespage3.map((feature, index) => (
                                                  <Feature
                                                    key={index}
                                                    icon={feature.icon}
                                                    title={feature.title}
                                                    text={feature.text}
                                                  />
                                                ))}
                                    </Carousel>}
                  </Box>
          </VStack>
    </Flex> 
  )
}


