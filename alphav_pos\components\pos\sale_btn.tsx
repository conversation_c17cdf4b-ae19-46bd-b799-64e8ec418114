"use client"
import { <PERSON><PERSON>,<PERSON>,GridItem,HStack,Text,Grid } from '@chakra-ui/react';
import { useState,memo, useEffect } from 'react';
import ViewPayment from "@/components/pos/viewpayment";
import {Transaction,Receipt} from '@/app/utils/definitions'
import {fetchData2,updateData} from '@/app/utils/pos';
import { toaster } from "@/components/ui/toaster";
import { string, z } from 'zod';
import { useAppDispatch, useAppSelector } from "@/lib/hooks";
import { updateUser } from "@/lib/features/users";


// const buttonDimensions = [{ width: "20rem", height:null },{ width: "13rem", height: null }, { width: "13rem", height: null }, ];

const buttonDimensions = [{ width: {base:"37%",md:"15rem"}, height:null },{ width: {base:"30%",md:"8rem"}, height: null }, { width: {base:"30%",md:"8rem"}, height: null }, ];

export const SaleButton = memo(({handlesetTrans,initializeTransaction,set_receipt,transaction_id,transaction,totals,Total_amount}:{handlesetTrans:(value: string) => void;initializeTransaction:()=>void;set_receipt: (Receipt:any) => void;transaction_id:any;transaction:any;totals:any;Total_amount:any}) => {
  const [isPaymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [myAction,setmyAction] = useState()
  const handleClose = () => {setPaymentDialogOpen(false);};
  const [transactionx, setTransactionx] = useState<Transaction[]>([]);
  const [Loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;


  const transactionSchema = z.object({
    trans_id: z.string().min(1, "Transaction not initlaized"),
    trans_status: z.number().min(0),
    trans_total: z.number().min(0),
    trans_quantity: z.number().min(0.1, "Quantity must be at least 0.1 or select and Item"),
    trans_tax: z.number().min(0),
    trans_net: z.number().min(0),
    trans_discount: z.number().min(0),
    shop_id: z.string(),
  });
  
  const handleActions = async (
    status: any,
    paymentMethod:string,
    amounts:any,
    phoneNumber:string
  ) => {
    setLoading(true);
    try{

        const tax = parseFloat(String(totals.find((t) => t.name == "Tax")?.amount || 0));
        const net = parseFloat(String(totals.find((t) => t.name == "Net")?.amount || 0));
        const discount = parseFloat(String(totals.find((t) => t.name == "Discount")?.amount ||0));
        const total = parseFloat(String(totals.find((t) => t.name == "Total")?.amount || 0));
        const quantity = parseFloat(String(totals.find((t) => t.name == "Total_Items")?.amount || 0));
        const newTransaction = {
          trans_id: transaction_id,
          trans_status: status,
          trans_total: total,
          trans_quantity:quantity,
          trans_tax: tax,
          trans_net: net,
          trans_discount: discount,
          shop_id: shop_id,
        };
      
        let sent_toback;
        if(status==15){
          const validatedTransaction = transactionSchema.parse(newTransaction);
          sent_toback = {
            ...validatedTransaction,
            paymentMethod:amounts,
            phoneNumber:phoneNumber
          }
          await updateData<Transaction>("transaction",transaction_id,sent_toback);
        }else{
          sent_toback = {
            ...newTransaction,
          }
          await updateData<Transaction>("transaction_",transaction_id,sent_toback);
        }
    
    }catch(error){
        toaster.create({
          title: 'Transaction Failed',
          description:
            error instanceof z.ZodError
              ? error.errors[0]?.message + " " + error.errors[0]?.path
              : 'An unexpected error occurred.',
          type: 'warning',
        });
      }
      finally{
        await  dispatch(updateUser({ ...user,transaction_id:""}));
        await  set_receipt([])
        await  handlesetTrans('')
        await  initializeTransaction()
      }
  };
  const fetchTransaction = async () => {
      try {
        const getTrans = await fetchData2<Transaction>('transaction', null, null, null, transaction_id);
        setTransactionx(getTrans);
    } catch (error) {
        toaster.error({title: "Error getting Transaction",description: 'An unexpected error occurred.'+error,});
      } 
    };

  useEffect(() => {if(!transaction)fetchTransaction();},[]);

  const HandleButtons = (action:number) => {
    setmyAction((prev) => prev !== action ? action : prev);
    setPaymentDialogOpen(true);
  };


  return (
    <div>
    <ViewPayment shop_id={shop_id} transaction={transaction?transaction:transactionx} isOpen={isPaymentDialogOpen} onClose={handleClose} onBuy={handleActions} mode={myAction} total={totals} Total_amount={Total_amount}/>
      <HStack  gap={2}>
          <Button  size="lg" colorPalette="teal" w={buttonDimensions[0]?.width} h={buttonDimensions[0]?.height}onClick={() => HandleButtons(1)}>Complete</Button>
          <Button  size="md" colorPalette="blue" w={buttonDimensions[2]?.width} h={buttonDimensions[2]?.height} onClick={()=>HandleButtons(2)}>Hold</Button>
          <Button  size="md" colorPalette="orange" w={buttonDimensions[1]?.width} h={buttonDimensions[1]?.height} onClick={()=>HandleButtons(3)}>Cancle</Button>
      </HStack>
    </div>
    );
});

export const ActionButtons = () => {

  



const [buttons, setButtons] = useState([
  { label: "Open Sales", color: "teal" },
  { label: "Close Sales", color: "yellow" },
  { label: "Open Orders", color: "orange" },
  { label: "Open Inventory", color: "orange" },
  { label: "Open Reports", color: "blue" },
  { label: "Back To sale", color: "purple" },
  { label: "Mpesa Messages", color: "red" },
]);

const addButton = () => {
  setButtons([...buttons, { label: `New Button ${buttons.length + 1}`, color: "gray" }]);
};

return (
  <Grid 
  // templateColumns="repeat(5, 1fr)"
  templateColumns="repeat(auto-fit, minmax(120px, 1fr))"
   gap={3}  w="100%">
    {buttons.map((btn, index) => (
      <GridItem key={index}>
        <Button
          size="md"
          boxShadow="xl"
          // bgColor={`${btn.color}.600`}
          color="white"
          // _hover={{ bgColor: `${btn.color}.300` }}
          w={'100%'}
        >{btn.label}
        </Button>
      </GridItem>
    ))}
    <GridItem>
      <Button
        size="md"
        boxShadow="xl"
        // bgColor="green.600"
        color="white"
        w={150}
        // _hover={{ bgColor: "green.300" }}
        onClick={addButton}
      >
        + Add Button
      </Button>
    </GridItem>
  </Grid>
);
};


