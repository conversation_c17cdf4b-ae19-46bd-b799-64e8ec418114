"use client"
import React,{useState,useEffect,memo,useMemo,useCallback} from 'react'
import {Box,Center,VStack,Table,Circle,Skeleton,IconButton,Badge} from '@chakra-ui/react';
import { toaster } from "@/components/ui/toaster"
import {Transaction,Receipt,ApiResponse} from '@/app/utils/definitions'
import {deleteData,fetchData2} from '@/app/utils/pos';
import { LuShoppingBag} from "react-icons/lu"
import { EmptyState } from "@/components/ui/empty-state";
import {SaleButton} from '@/components/pos/sale_btn';
import Total_s from '@/components/pos/total'
import { MdDeleteForever } from "react-icons/md";
interface ReceiptProps {
  handlesetTrans: (value: string) => void; 
  initializeTransaction?:()=>void;
  setTotals?:()=>void;
  transaction_id?: string;
  latestReceipt?: Receipt[];
  shop_id?: string;
  transaction:any;
  checkTransaction:(status:any,trans:any)=>void;
}

const Receiptpage =  memo(({handlesetTrans,initializeTransaction,setTotals,transaction_id,latestReceipt, shop_id,transaction,checkTransaction}:ReceiptProps) => {
  const [receipt, setReceipt] = useState<Receipt[]>([]);
  const [Loading, setLoading] = useState(false);
  const [Tamount,setTamount] = useState(null);

  // const [transaction, setTransaction] = useState<Transaction[]>([])
  const fetchReceipts =useCallback(async () => {
      if (!transaction_id) return;
      try { 
        const queryParams = { shop_id };
        const newReceipt = await fetchData2<Receipt>('receipt_trans', null, null,queryParams,transaction_id);
        setReceipt(newReceipt);
      } catch (error) {
       if (error.message == "403") {
        checkTransaction('receipt',transaction_id);
      }
        toaster.create({title: 'Failed to get receipt.'});
      }
  }, [transaction_id]);

  useEffect(() => {
    if (latestReceipt) {
      setLoading(true);
      setTimeout(() => {setReceipt(prev => [latestReceipt, ...prev]); setLoading(false);}, 500); 
    }
  }, [latestReceipt]);
  
  useEffect(() => {
    if(transaction_id)fetchReceipts();},[transaction_id]);


  const totals = useMemo(() => {
    const total = receipt?.reduce((sum, item) => sum + (Number(item.receipt_total) || 0), 0);
    const totalQuantity = receipt?.reduce((sum, item) => sum + (Number(item.receipt_quantity) || 0), 0);
    const tax = 0; 
    const discount = 0;
    const net = total - tax - discount;
    const floatTotal = parseFloat(total).toFixed(2); 
    if (parseFloat(floatTotal) > 0) setTamount(floatTotal);

    return [
      { name: "Tax", amount: tax.toFixed(2) },
      { name: "Net", amount: net.toFixed(2) },
      { name: "Discount", amount: discount.toFixed(2) },
      { name: "Total", amount: total.toFixed(2) },
      { name: "Total_Items", amount: totalQuantity },
    ];


  }, [receipt]); 

  const handleDeleteReceipt = async (receiptId: string) => {
    try {
      await deleteData('receipt',receiptId);
      setReceipt((prev) => prev.filter((item) => item.receipt_id != receiptId));
      toaster.create({ title: "Receipt deleted successfully", type: "success" });
    } catch (error) {
      toaster.create({ title: "Failed to delete receipt", description: error.message, type: "error" });
    }
  };


  return (
    <VStack gap={4} align="stretch" >
    <>
    <Table.ScrollArea h={350} w="100%"  borderRadius="lg" boxShadow="xl">
      <Table.Root size={{ base: "sm", md: "md" }} stickyHeader striped>
        <Table.Header>
          <Table.Row bg="bg.subtle">
            <Table.ColumnHeader>Item</Table.ColumnHeader>
            {/* <Table.ColumnHeader>Code</Table.ColumnHeader> */}
            <Table.ColumnHeader>Qty</Table.ColumnHeader>
            <Table.ColumnHeader>Each</Table.ColumnHeader>
            <Table.ColumnHeader textAlign="end">Total</Table.ColumnHeader>
            <Table.ColumnHeader>Action</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>

        <Table.Body>
        {Loading && latestReceipt && (
            <Table.Row>
              <Table.Cell>
                <Skeleton height="20px" width="80%" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="50%" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="40px" />
              </Table.Cell>
              <Table.Cell>
                <Skeleton height="20px" width="60px" />
              </Table.Cell>
              <Table.Cell textAlign="end">
                <Skeleton height="20px" width="80px" />
              </Table.Cell>
            </Table.Row>
          )}
          {receipt.length == 0 ? (
              <Table.Row>
                <Table.Cell colSpan={5} textAlign="center">
                  <Center h='21rem'>
                    <EmptyState
                      icon={<LuShoppingBag />}
                      title="Empty counter"
                      description="Search and input Quanity"
                    />
                  </Center>
                </Table.Cell>
              </Table.Row>
            ) : (
              <>
              {receipt.map((item) => (
                <Table.Row key={item.receipt_id}>
                  <Table.Cell>{item.receipt_item}</Table.Cell>
                  {/* <Table.Cell>{item.code}</Table.Cell> */}
                  <Table.Cell>{item.receipt_quantity}</Table.Cell>
                  <Table.Cell>{item.receipt_each}</Table.Cell>
                  <Table.Cell textAlign="end">{item.receipt_total}</Table.Cell>
                  <Table.Cell>
                      <IconButton
                        aria-label="Delete receipt"
                        color="red"
                        size="sm"
                        variant={"ghost"}
                        onClick={() => handleDeleteReceipt(item.receipt_id)}><MdDeleteForever size="md"/></IconButton>
                  </Table.Cell>
                </Table.Row>
              ))}
            </>)}
            
        </Table.Body>

      </Table.Root>
    </Table.ScrollArea>

          <Box borderRadius="lg" boxShadow="xl" p={4} position="relative">
                  <Box position="absolute" top="0.1rem" left="0.3rem">
                      <Badge variant="solid" colorPalette="blue">{transaction_id}</Badge>
                  </Box>

                  <Box position="absolute" top="-10px" right="-10px">
                      <Circle size="2rem" bg="red.500" color="white" fontSize="md">{totals.find((item) => item.name == "Total_Items")?.amount || 0}</Circle>
                  </Box>
                <Total_s totals={totals}/>
          </Box>
        </>
        <SaleButton  handlesetTrans={handlesetTrans}  initializeTransaction={initializeTransaction} set_receipt={setReceipt} transaction_id={transaction_id} transaction={transaction} totals={totals} Total_amount ={Tamount}/>
       </VStack>
  )
})

export default  Receiptpage;
