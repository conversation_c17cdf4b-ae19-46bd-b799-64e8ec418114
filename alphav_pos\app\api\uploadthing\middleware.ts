import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { AppStore, RootState } from "@/lib/store";

export async function middleware(req: NextRequest) {
  const store = req.headers.get("x-store") as unknown as AppStore;
  const state = store.getState() as RootState;
  const user = state.user.currentUser;

  if (!user || !user.authenticated) {
    return new NextResponse(null, {
      status: 401,
      statusText: "Unauthorized",
    });
  }

  return NextResponse.next();
}

export const config = {
  matcher: "/api/uploadthing/:path*",
};
