import { Radio, RadioGroup } from "@/components/ui/radio"
import { Collapsible,Button,Heading,VStack,Checkbox,Stack,HStack, Text, Box,Center,Spinner,Input,IconButton } from "@chakra-ui/react";
import React,{useState,useEffect,memo} from 'react'
import {insertBulk,fetchData,fetchData2,insertData,updateData,deleteData,fetchImage } from '@/app/utils/pos';
import{Payment_Method,Transaction} from '@/app/utils/definitions'
import { SlRefresh,SlArrowRight } from "react-icons/sl";


interface ViewpaymentProps {
  shop_id:string;
  transaction: Transaction;
  onClose: () => void; 
  onBuy: (status:number,paymentMethod:string,phoneNumber:string,amounts:any) => void;
  paid_status:number;
  total:any
  Total_amount:any
}



const Payment = memo(({ shop_id, transaction, onClose, onBuy, paid_status,total,Total_amount}:ViewpaymentProps) => {
  const [selectedMethods, setSelectedMethods] = useState([]);
  const [amounts, setAmounts] = useState({});
  const [Loading, setLoading] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [phoneNumber, setPhoneNumber] = useState("");
  const totalAmount = Total_amount || 0;
  const [initiate, setInitate] = useState(true);
  const [isLoading, setisLoading] = useState(false);

  const [isLoadingx, setisLoadingx] = useState(false);

  useEffect(() => {
    fetchPayments();
  }, []);

  const fetchPayments = async () => {
    setLoading(true);
    try {
      const methods = await fetchData2("paymeth", null, null, null, shop_id);
      setPaymentMethods(methods);
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleMethodSelection = (method) => {
    let updatedMethods = [...selectedMethods];
    let updatedAmounts = { ...amounts };
    
    if (updatedMethods.includes(method.payment_method_id)) {
      updatedMethods = updatedMethods.filter(id => id !== method.payment_method_id);
      delete updatedAmounts[method.payment_method_id];
    } else {
      updatedMethods.push(method.payment_method_id);
      const remaining = totalAmount - Object.values(updatedAmounts).reduce((a, b) => a + b, 0);
      updatedAmounts[method.payment_method_id] = remaining;
    }
    setSelectedMethods(updatedMethods);
    setAmounts(updatedAmounts);
  };

  const handleAmountChange = (methodId, value) => {
    let newAmount = Math.max(0, Math.min(totalAmount, Number(value)));
    let updatedAmounts = { ...amounts, [methodId]: newAmount };
    const totalPaid = Object.values(updatedAmounts).reduce((a, b) => a + b, 0);
    if (totalPaid > totalAmount) {
      updatedAmounts[methodId] = totalAmount - (totalPaid - newAmount);
    }
    setAmounts(updatedAmounts);
  };

  const CallbackPayments = async (code:any) => {
    setisLoading(true);
    const query = {
      query: {
        business_shortcode: code,
        trans_amount: "500",
        bill_ref_number: "INV123",
        trans_id: ["TX123", "TX456"],
        msisdn: ["254700123456"],
        first_name: ["John", "Doe"]
      }
    };
    try {
      const methods_ = await fetchData2<Transaction>("paymeth_mpesa",null,null,query,code);
      const methods = await fetchData2<Transaction>("paymeth_callback",null,null,query,code);
      console.log(methods)
      
      // if (methods?.length > 0) {
      //   console.log(methods)
      // }
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    } finally {
      setisLoading(false);
    }
  };


  const totalPaid = Object.values(amounts).reduce((a, b) => a + b, 0);

  const initiatePayments = async (amount:any,method_id:any) => {
    setisLoading(true);

    try {
      var data_submit ={
        phoneNumber:phoneNumber,
        paymentMethodId:method_id,
        trans_status:18,
        trans_total:amount,
        trans_id:transaction?.data?.trans_id

      }
      const ApiResponse = await insertData("paymeth_submit",data_submit);
      // console.log(ApiResponse)
      if(ApiResponse.paymentResponse?.success){
          setInitate(false)
      }
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    } finally {
      setisLoading(false);
    }
  };

  useEffect(() => {
    if (totalPaid != totalAmount)setInitate(true);

    if (selectedMethods.length > 1) {
      setInitate(true);
    } else if (selectedMethods.length == 1) {
      const payment_name = paymentMethods.find((m) => selectedMethods.includes(m.payment_method_id));
      if (payment_name?.payment_method_name == "cash" && totalPaid == totalAmount) {
        setInitate(false);
      } else {
        setInitate(true);
      }
    }
  }, [totalPaid, totalAmount,selectedMethods]);
  

  return (
    <VStack gap={6} align="stretch">
      <Text fontSize="xl" fontWeight="bold" textAlign="center">
        Payment Method
      </Text>
      {Loading ? (
        <Center>
          <Spinner size="xl" color="red.600"/>
        </Center>
      ) : (
        <VStack gap={4} align="stretch">
          {paymentMethods.map((method) => (
            <Box key={method.payment_method_id} p={4} borderWidth="1px" borderRadius="lg" cursor="pointer" _hover={{ borderColor: "blue.500" }}>
              <HStack justifyContent="space-between" onClick={() => handleMethodSelection(method)}>
                <Stack direction='row'>
                    <Checkbox.Root  style={{ cursor: "pointer" }} onClick={(e) => e.stopPropagation()} variant={'outline'} colorPalette='blue' checked={selectedMethods.includes(method.payment_method_id)} onCheckedChange={() => handleMethodSelection(method)}>
                      <Checkbox.HiddenInput />
                      <Checkbox.Control>
                        <Checkbox.Indicator />
                      </Checkbox.Control>
                    </Checkbox.Root>
                    {/* <Heading>{method.payment_method_name }</Heading> */}
                    <Heading>
                      {method.payment_method_name === "mpesa_express"
                        ? "Mpesa STK"
                        : method.payment_method_name === "mpesa_till_paybill"
                        ? "Mpesa Till/Paybill"
                        : method.payment_method_name}
                    </Heading>

                </Stack>
                <Text>{method.payment_number ? `(${method.payment_number})` : null}</Text>
              </HStack>
              {selectedMethods.includes(method.payment_method_id) && (
                <Stack>
                  <HStack>
                        <Input
                          mt={2}
                          type="number"
                          value={amounts[method.payment_method_id] || ""}
                          onChange={(e) => handleAmountChange(method.payment_method_id, e.target.value)}
                          placeholder="Enter amount"
                          borderColor="gray.300"
                        />
                        {method.payment_method_name?.toLowerCase() == 'mpesa_till_paybill' &&(
                                  <VStack>
                                  <IconButton
                                    aria-label="call_refreash"
                                    rounded="full"
                                    size="xs"
                                    style={{ boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)", transition: "transform 0.2s ease-in-out" }}
                                    onMouseEnter={(e) => (e.currentTarget.style.transform = "scale(1.1)")}
                                    onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
                                    onClick={() => CallbackPayments(method.payment_number)}
                                      disabled={isLoading}
                                  >
                                    <SlRefresh /> 
                                  </IconButton><Text>{isLoading? 'Loading...':'refresh'}</Text>
                                
                                  </VStack>
                                )}
                        {method.payment_method_name?.toLowerCase() == "mpesa_express" && (initiate ? (
                              <VStack>
                              <IconButton
                                aria-label="call_refreash"
                                rounded="full"
                                size="xs"
                                style={{ boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)", transition: "transform 0.2s ease-in-out" }}
                                onMouseEnter={(e) => (e.currentTarget.style.transform = "scale(1.1)")}
                                onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
                                onClick={() => initiatePayments(amounts[method.payment_method_id],method.payment_method_id)}
                              >
                                <SlArrowRight />
                              </IconButton><Text>submit</Text>
                              </VStack>
                           ) : (
                            <VStack>
                            <IconButton
                              aria-label="call_refreash"
                              rounded="full"
                              size="xs"
                              style={{ boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)", transition: "transform 0.2s ease-in-out" }}
                              onMouseEnter={(e) => (e.currentTarget.style.transform = "scale(1.1)")}
                              onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
                              onClick={() => CallbackPayments(method.payment_number)}

                            >
                              <SlRefresh /> 
                            </IconButton><Text>refresh</Text>
                          
                            </VStack>
                         ))}
                    </HStack>
                    {method.payment_method_name?.toLowerCase() === "mpesa_express" && (
                          <Box mt={2}>
                            <Input
                              placeholder="Enter phone number (07xxxxxxxx / 01xxxxxxxx)"
                              value={phoneNumber}
                              onChange={(e) => setPhoneNumber(e.target.value)}
                              borderColor={/^(?:07|01)\d{8}$/.test(phoneNumber) ? "gray.300" : "red.500"}
                              _hover={{ borderColor: "blue.500" }}
                            />
                            {!/^(?:07|01)\d{8}$/.test(phoneNumber) && phoneNumber.length > 0 && (
                              <Text color="red.500" fontSize="sm">
                                Invalid phone number. Use 07xxxxxxxx or 01xxxxxxxx.
                              </Text>
                            )}
                          </Box>
                        )}

                  </Stack>          
              )}
            </Box>
          ))}
        </VStack>
      )}
      <Heading><Text color={'blue.600'}>Total Amount: </Text>{totalPaid} / {totalAmount}</Heading>
      <HStack justifyContent="flex-end" mt={4}>
        <Button onClick={async() => {
          try{
            await setisLoadingx(true)
           const formattedAmounts = Object.entries(amounts).map(([paymentMethodId, amount]) => ({
            paymentMethodId,
            amount,
          }));
          await onBuy(paid_status,selectedMethods, formattedAmounts, phoneNumber)
        }catch(error){
            console.log(error)
        }finally{
          await setisLoadingx(false)
          await onClose()
        }
        }
        } disabled={initiate || isLoadingx}>
          {isLoadingx ? "Saving ...":"Confirm Payment"}
        </Button>
        <Button variant="outline" onClick={onClose}>Cancel</Button>
      </HStack>
    </VStack>
  );
});



export default Payment;
