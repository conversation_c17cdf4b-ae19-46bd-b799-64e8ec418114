import {Stack} from '@chakra-ui/react';
import Table from '@/components/pos/Table_default'
import { useState } from "react";
const Completed = () => {

    const columns = [
        { label: 'Trans_id', key: 'name', align: 'left' },
        { label: 'Items', key: 'code', align: 'left' },
        { label: 'Qty', key: 'quantity', align: 'center' },
        { label: 'Total', key: 'price', align: 'center'},
        { label: 'Action', key: 'actions', align: 'center' }
      ];
      
      const columnSizes = {
        base: 'sm',
        md: 'md'
      };
      
      const data = [
        { id: 1, name: 'Item A', code: 'A001', quantity: 2, price: 4000 },
        { id: 2, name: 'Item B', code: 'B002', quantity: 1, price: 300000 },
        { id: 3, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 4, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 5, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 6, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 7, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 8, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 9, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id:10, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 11, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 12, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 13, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 14, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 15, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 17, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 18, name: 'Item C', code: 'C003', quantity: 5, price: 75 },
        { id: 19, name: 'Item C', code: 'C003', quantity: 5, price: 75 },

      ];
      
      const totalPages = Math.ceil(data.length / 15);
      const [currentPage, setCurrentPage] = useState(1);
      
      const handlePageChange = (newPage) => {
        setCurrentPage(newPage);
      };
      const getActions = (item) => [
        {label: 'View',colorScheme: 'blue',onClick: () => console.log(`View ${item.name}`),},
        {label: 'Edit',colorScheme: 'green',onClick: () => console.log(`Edit ${item.name}`),},
        // {label: 'Delete',colorScheme: 'red',onClick: () => console.log(`Delete ${item.name}`),},
      ];
      
  return (
    <Stack direction='column'>
        <Table  columns={columns} columnSizes={columnSizes} data={data} height='auto' width='auto' currentPage={currentPage} totalPages={totalPages} handlePageChange={handlePageChange} pageSize={10}  getActions={getActions}/>
  </Stack>
  );
};

export default Completed;