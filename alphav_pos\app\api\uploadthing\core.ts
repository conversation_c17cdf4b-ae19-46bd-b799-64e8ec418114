import { createUploadthing, type FileRouter } from 'uploadthing/next'
import { UTApi } from "uploadthing/server";

const f = createUploadthing()

export const ourFileRouter = {
  imageUploader: f({
    image: { maxFileSize: '2GB' }
  })
    .onUploadComplete(async ({ file }) => {
      try {
        return { file: file.ufsUrl };
      } catch (error) {
        throw error;
      }
    }),
} satisfies FileRouter

export type OurFileRouter = typeof ourFileRouter


export const utapi = new UTApi({
  token: process.env.UPLOADTHING_TOKEN, // Or hardcode a token if you dare
  // Optional extras:
  // logLevel: "debug",
  // logFormat: "pretty",
  // apiUrl: "https://api.uploadthing.com", // Only change for self-hosting/testing
  // ingestUrl: "https://uploadthing.com/ingest", // Usually inferred from token
});
