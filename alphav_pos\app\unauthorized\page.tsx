'use client';

import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { getDefaultRoute, getAllowedRoutes } from '@/app/utils/rbac';
import {
  Box,
  Button,
  Center,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Card
} from '@chakra-ui/react';
import { LuShieldAlert, LuContainer, LuLogOut } from 'react-icons/lu';
import { useDispatch } from 'react-redux';
import { logout } from '@/lib/features/users';

export default function UnauthorizedPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  const handleGoToDashboard = () => {
    if (currentUser) {
      const defaultRoute = getDefaultRoute(currentUser.roles);
      router.push(defaultRoute);
    } else {
      router.push('/');
    }
  };

  const handleLogout = () => {
    dispatch(logout());
    router.push('/');
  };

  const allowedRoutes = currentUser ? getAllowedRoutes(currentUser.roles) : [];

  return (
    <Center minH="100vh"  p={4}>
      <Card.Root maxW="md" w="full" p={8} textAlign="center">
        <VStack gap={6}>
          {/* Error Icon */}
          <Box fontSize="6xl" color="red.500">
            <LuShieldAlert />
          </Box>

          {/* Error Message */}
          <VStack gap={2}>
            <Heading size="xl" color="red.600">
              Access Denied
            </Heading>
            <Text color="gray.600" fontSize="lg">
              You don't have permission to access this page.
            </Text>
          </VStack>

          {/* User Info */}
          {currentUser && (
            <Box p={4}  borderRadius="md" w="full">
              <VStack gap={2}>
                <Text fontSize="sm" color="gray.600">
                  Logged in as:
                </Text>
                <HStack>
                  <Text fontWeight="bold">{currentUser.username}</Text>
                  <Badge colorScheme="blue" textTransform="capitalize">
                    {currentUser.roles.role_name}
                  </Badge>
                </HStack>
              </VStack>
            </Box>
          )}

          {/* Allowed Routes */}
          {allowedRoutes.length > 0 && (
            <Box w="full">
              <Text fontSize="sm" color="gray.600" mb={2}>
                You have access to:
              </Text>
              <VStack gap={1}>
                {allowedRoutes.map((route) => (
                  <Button
                    key={route}
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push(route)}
                    w="full"
                  >
                    {route.replace('/pos/', '').replace('/pos', 'Dashboard')}
                  </Button>
                ))}
              </VStack>
            </Box>
          )}

          {/* Action Buttons */}
          <VStack gap={3} w="full">
            <Button
              colorScheme="blue"
              onClick={handleGoToDashboard}
              w="full"
            ><LuContainer />
              Go to Dashboard
            </Button>
            
            <Button
              variant="outline"
              onClick={handleLogout}
              w="full"
            >
              <LuLogOut />Logout
            </Button>
          </VStack>

          {/* Help Text */}
          <Text fontSize="xs" color="gray.500" textAlign="center">
            If you believe this is an error, please contact your administrator.
          </Text>
        </VStack>
      </Card.Root>
    </Center>
  );
}
