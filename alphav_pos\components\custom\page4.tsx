
'use client';
import React, { useState, useEffect } from 'react';
import { VStack, Flex, Heading, Text, useBreakpointValue,Box } from '@chakra-ui/react';

const venueLocation = { lat: process.env.NEXT_PUBLIC_LAT, lng: process.env.NEXT_PUBLIC_LNG };
const Directions = () => {
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          });
        },
        (error) => {
          console.error("Error getting location:", error.message);
        }
      );
    } else {
      console.error("Geolocation is not supported by this browser.");
    }
  }, []);
  const directionsUrl =userLocation ?`${process.env.NEXT_PUBLIC_DIRECT_URL}key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&origin=${userLocation.lat},${userLocation.lng}&destination=${venueLocation.lat},${venueLocation.lng}`
      :`${process.env.NEXT_PUBLIC_PLACE_URL}key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&q=${venueLocation.lat},${venueLocation.lng}`;
  const ThreeDurl =`${process.env.NEXT_PUBLIC_STREET}key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&location=${venueLocation.lat},${venueLocation.lng}`;
  
  return (
    
      <VStack  pt={{base:'0',md:'70px'}}>
        <Heading fontFamily="Caveat" textStyle="6xl" textAlign="center">Where to Find Us</Heading>
        <Flex direction={{ base: 'column', md: 'row' }}>
          <Box>
          {error && <Text color="pink.500" textAlign="center">{error}</Text>}
          {/* {userLocation && ( */}
          <Flex direction="column"  p={6} width={{ base: "100vw", md: "60%", lg: "50rem" }}  height={{ base: "20rem", md: "30rem", lg: "35rem" }} borderRadius="md" boxShadow="md" > 
            <iframe src={directionsUrl} width="100%" height="100%" ></iframe>
          </Flex>   
          {/* )} */}
          </Box>
          <Box>
          {error && <Text color="pink.500" textAlign="center">{error}</Text>}
          {/* {userLocation && ( */}
          <Flex direction="column"  p={6} width={{ base: "100vw", md: "60%", lg: "50rem" }}  height={{ base: "20rem", md: "30rem", lg: "35rem" }} borderRadius="md" boxShadow="md" > 
            <iframe src={ThreeDurl} width="100%" height="100%" ></iframe>
          </Flex>   
          {/* )} */}
          </Box>
        </Flex> 
      </VStack>
  );
};

export default Directions;
