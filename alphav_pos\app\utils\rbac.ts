// Role-Based Access Control (RBAC) utility
export type UserRole = 'admin' | 'manager' | 'pos' | 'supervisor' | 'inventory' | 'supplies' | 'reports'|'user';

export interface RoutePermission {
  path: string;
  allowedRoles: UserRole[];
  exact?: boolean;
}

// Define route permissions
export const ROUTE_PERMISSIONS: RoutePermission[] = [
  // Admin can access everything
  { path: '/pos', allowedRoles: ['admin', 'manager', 'pos', 'supervisor', 'inventory', 'supplies', 'reports', 'user'], exact: false },

  // Route permissions - consolidated
  { path: '/pos/sale', allowedRoles: ['admin', 'manager', 'pos', 'supervisor', 'user'], exact: false },
  { path: '/pos/inventory', allowedRoles: ['admin', 'manager', 'supervisor', 'inventory'], exact: false },
  { path: '/pos/stock', allowedRoles: ['admin', 'manager', 'supervisor', 'inventory', 'supplies'], exact: false },
  { path: '/pos/reports', allowedRoles: ['admin', 'manager', 'reports', 'supervisor'], exact: false },
  { path: '/pos/manager', allowedRoles: ['admin', 'manager', 'supervisor'], exact: false },
  { path: '/pos/reports/suppliers', allowedRoles: ['admin', 'manager', 'supplies', 'reports', 'supervisor'], exact: false },
];

// Helper function to extract role names from role objects or strings
const extractRoleNames = (roles: any[]): string[] => {
  return roles.map(role => {
    if (typeof role == 'string') {
      return role;
    } else if (role && typeof role == 'object' && role.role_name) {
      return role.role_name;
    }
    return 'user'; 
  });
};

// Check if user has permission to access a route
export const hasRoutePermission = (
  userRoles: string[] | UserRole[] | any[],
  routePath: string
): boolean => {
  // Extract role names from role objects if needed
  const roleNames = extractRoleNames(userRoles);

  // Admin has access to everything
  if (roleNames.includes('admin')) {
    return true;
  }

  // Find matching route permission
  const matchingPermission = ROUTE_PERMISSIONS.find(permission => {
    if (permission.exact) {
      return permission.path == routePath;
    } else {
      return routePath.startsWith(permission.path);
    }
  });

  if (!matchingPermission) {
    // If no specific permission found, grant access for authenticated users
    return true;
  }

  // Check if user has any of the allowed roles
  const hasPermission = matchingPermission.allowedRoles.some(role =>
    roleNames.includes(role)
  );

  return hasPermission;
};

// Get user's primary role (highest priority)
export const getPrimaryRole = (roles: string[] | UserRole[] | any[]): UserRole | null => {
  const roleNames = extractRoleNames(roles);
  const rolePriority: UserRole[] = ['admin', 'manager', 'supervisor', 'pos', 'inventory', 'supplies', 'reports'];

  for (const role of rolePriority) {
    if (roleNames.includes(role)) {
      return role;
    }
  }

  return roleNames.length > 0 ? roleNames[0] as UserRole : null;
};

// Get allowed routes for a user based on their roles
export const getAllowedRoutes = (userRoles: string[] | UserRole[] | any[]): string[] => {
  const roleNames = extractRoleNames(userRoles);

  if (roleNames.includes('admin')) {
    return ['/pos', '/pos/sale', '/pos/inventory', '/pos/stock', '/pos/reports', '/pos/manager'];
  }

  const allowedRoutes: string[] = [];

  ROUTE_PERMISSIONS.forEach(permission => {
    if (permission.allowedRoles.some(role => roleNames.includes(role))) {
      if (!allowedRoutes.includes(permission.path)) {
        allowedRoutes.push(permission.path);
      }
    }
  });

  // Ensure manager and supervisor users get access to manager route
  if ((roleNames.includes('manager') || roleNames.includes('supervisor')) && !allowedRoutes.includes('/pos/manager')) {
    allowedRoutes.push('/pos/manager');
  }

  return allowedRoutes;
};

// Get default route for user based on their primary role
export const getDefaultRoute = (userRoles: string[] | UserRole[] | any[]): string => {
  const primaryRole = getPrimaryRole(userRoles);

  switch (primaryRole) {
    case 'admin':
      return '/pos/manager';
    case 'manager':
      return '/pos/manager';
    case 'pos':
      return '/pos/sale';
    case 'supervisor':
      return '/pos/manager';
    case 'inventory':
      return '/pos/inventory';
    case 'supplies':
      return '/pos/reports/suppliers';
    case 'reports':
      return '/pos/reports';
    default:
      return '/pos/sale';
  }
};

// Check if user is authenticated and has valid roles
export const isUserAuthorized = (user: any): boolean => {
  if (!user || !user.authenticated) {
    return false;
  }

  // If user doesn't have roles, they're still authorized if authenticated
  if (!user.roles || !Array.isArray(user.roles) || user.roles.length == 0) {
    return true; // Allow authenticated users even without specific roles
  }

  return true;
};
