'use client'
import {Table, Container, SimpleGrid, Flex, Heading, Text, Stack, Icon,VStack,Box,HStack,createIcon,} from '@chakra-ui/react'
import { useColorModeValue } from "@/components/ui/color-mode"
import {ourstory} from '@/utils/data'
import { ReactElement,ReactNode } from 'react'
import {} from '@/utils/data'
import {features_page2,caveat,MotionBox,variants, fromRight,fromLeft, MotionStack,MotionImage,MotionSimpleGrid,MotionFlex} from '@/utils/data'

interface FeatureProps {
  text: string
  icon?: ReactElement
}

const Feature = ({ text,icon }: FeatureProps) => {
  return (
    <Stack direction={'row'} align="flex-start">
      <Text fontWeight={600} textAlign={'left'}>{text}</Text>
    </Stack>
  )
}


export default function page2() {
  return (
    <Flex> 
      <MotionSimpleGrid columns={{ base: 1, md: 2 }} variants={variants} initial ="hidden" animate="show" >
        <MotionStack gap={4}  p={5}  pt={{base:'0',md:'70px'}} variants={fromLeft}>
          <Heading size="6xl" className={caveat.className} >Welcome to Alpha's</Heading>
      <Flex p={3}><Text color={useColorModeValue('gray.700', 'gray.400')} fontSize={'lg'} >{ourstory}</Text></Flex>
          <SimpleGrid columns={{ base: 1, md: 2 }}>
                <Flex   
                position="relative" 
                mb={{ base: "150px", md: 0 }} 
                >
                  <Text textStyle="4xl" className={caveat.className} position={'absolute'} transform={'rotate(-10deg)'}>All for the Baby</Text>
                  <Arrow 
                  color={useColorModeValue('gray.800', 'gray.300')} 
                  w={150} h={150} left={20} 
                  transform={{ base: 'matrix(-1,0,0,-1,0,0)', md: 'matrix(-1,0,0,1,0,0)' }}
                  position={'absolute'}
                  />
                </Flex>

                <Flex>
                  <Table.Root variant="line" size="sm"   style={{ borderCollapse: "collapse" }}>
                        <Table.Body>
                          {features_page2.map((feature, index) => (
                              <Table.Row key={index}>
                                <Table.Cell>
                                  <Feature icon={feature.icon} text={feature.text} />
                                </Table.Cell>
                              </Table.Row>
                          ))}
                        </Table.Body>
                      </Table.Root>
                      </Flex>
              </SimpleGrid>
        </MotionStack>

        <Flex>
        <Box position="relative" width="100%" height="100vh" rounded="md" overflow="hidden">
          <MotionImage variants={fromRight}
            alt={'feature image'}
            src="/10.png"
            fill
            // sizes="(max-width: 450px) 100vw, 450px" 
            style={{ objectFit: 'contain',height:'100%',width:'100%'}}
          />
          </Box>
        </Flex>
      </MotionSimpleGrid>
  </Flex>
  )
}

export const Arrow  = createIcon({
  displayName: 'Arrow',
  viewBox: '0 0 72 24',
 
  path: (
    <path d="M.6 7.082a.75.75 0 0 1 .667-.26c1.565.197 3.936.424 6.278.418 2.38-.007 4.59-.255 5.925-.919a.746.746 0 1 1 .665 1.336c-1.672.831-4.194 1.068-6.586 1.075a50 50 0 0 1-5.25-.29c.51 1.154 1.21 2.198 1.948 3.296l.446.669c1.102 1.666 2.188 3.492 2.425 5.857a.747.747 0 0 1-1.487.149c-.198-1.978-1.101-3.545-2.185-5.183q-.21-.317-.43-.642C2.06 11.166 1.025 9.627.459 7.781a.75.75 0 0 1 .143-.7m4.92 3.143a.75.75 0 0 1 1.05-.123c9.913 7.832 23.346 12.304 35.508 11.375C54.2 20.55 65.036 14.274 69.977.583a.748.748 0 0 1 1.406.505C66.213 15.415 54.808 22 42.193 22.964c-12.573.96-26.37-3.65-36.549-11.692a.745.745 0 0 1-.122-1.048" fill="currentColor"/>
  ),
});
