"use client"
import React, { useState, useEffect, useCallback, Suspense, useRef } from 'react'
import { formatCurrency } from '@/app/utils/actions'
import { Text, Stack,List,SimpleGrid,Icon,Separator, Spinner,Accordion, Box, Center, Card, VStack,Field, HStack, Input, Table, Circle, Heading, Flex, Button, Grid, GridItem, Tabs, Select,Badge, Checkbox, CheckboxGroup, Switch } from '@chakra-ui/react';
import { RadioGroup } from "@chakra-ui/react"
import Image from 'next/image'
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Shop, Product, Transaction, Order, Receipt, Category, Discount, ApiResponse, Status } from '@/app/utils/definitions'
import { insertBulk, fetchData, fetchData2, insertData, updateData, deleteData, fetchImage,baseApiUrl } from '@/app/utils/pos';
import { z } from 'zod';
import {FileUploadDropzone,FileUploadList,FileUploadRoot,FileUploadTrigger} from "@/components/ui/file-upload"
import { useAppDispatch, useAppSelector } from "@/lib/hooks";
import { toaster } from "@/components/ui/toaster"
import { StatHelpText, StatLabel, StatRoot, StatUpTrend, StatValueText } from "@/components/ui/stat"
import { FiDownload, FiPrinter,FiCheck, FiCalendar, FiBarChart2, FiDollarSign,FiShoppingCart, FiPackage, FiTrendingUp, FiPlus, FiUpload, FiFile, FiX } from 'react-icons/fi';

import { motion } from "motion/react"
import { useColorModeValue } from '@/components/ui/color-mode';
import Search from '@/components/pos/search'
import Table_default from '@/components/pos/Table_default1'
import { NativeSelectField, NativeSelectRoot } from "@/components/ui/native-select"
import { SelectContent, SelectItem, SelectRoot, SelectTrigger, SelectValueText } from "@/components/ui/select"
import { DialogBody, DialogCloseTrigger, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { log } from 'console';

// Define interfaces for supplier data
interface Supplier {
  supplier_branch: any;
  supplier_id: string;
  supplier_name: string;
  supplier_contact: string;
  supplier_email: string;
  supplier_address: string;
  supplier_status: string;
  created_at: string;
}

interface SupplyItem {
  available: number;
  trans_id: any;
  destination: any;
  receipt_id: any;
  supply_id: string;
  supplier_id: string;
  destination_id: string;
  item_name: string;
  quantity: number;
  amount_expected: number;
  amount_paid: number;
  supply_date: string;
  attachments: string[];
  status: string;
  quantity_sold?: number;
  selling_price?: number;
  payment_status?: string;
  amount_due?: number;
  transaction?: {
    supply_receipts: any;
    trans_id: string;
    receipts: Array<{
      receipt_id: string;
      receipt_item: string;
      receipt_quantity: number;
      receipt_each: number | null;
      receipt_total: number;
      receipt_net: number;
      trans_id: string;
      shop_id: string;
    }>
}
}

interface SupplySale {
  supply_branch: React.JSX.Element;
  invoce_no: ReactNode;
  sale_id: string;
  supply_id: string;
  quantity_sold: number;
  selling_price: number;
  total_amount: number;
  sale_date: string;
  customer_name?: string;
  customer_contact?: string;
  payment_method?: string;
  payment_status: string;
  is_supplier?: boolean;
  buyer_supplier_id?: string;
}

interface PaymentRecord {
  id: string;
  date: string;
  amount: number;
  attachments: string[];
  status: string;
}

// Infinite Scroll Dropdown Component
interface InfiniteScrollDropdownProps {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  name: string;
  shop_id: string;
  endpoint: string;
  displayField: string;
  valueField: string;
  bg?: string;
  color?: string;
  borderColor?: string;
  _hover?: any;
  _focus?: any;
}

const InfiniteScrollDropdown: React.FC<InfiniteScrollDropdownProps> = ({
  value,
  onChange,
  placeholder,
  name,
  shop_id,
  endpoint,
  displayField,
  valueField,
  bg,
  color,
  borderColor,
  _hover,
  _focus
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOption, setSelectedOption] = useState<any>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const pageSize = 10;

  // Load options from API using existing fetchData function
  const loadOptions = useCallback(async (pageNum: number = 1, query: string = '', reset: boolean = false) => {
    if (loading) return;

    setLoading(true);
    try {
      // Use the existing fetchData function that's already working
      const response = await fetchData(endpoint, pageNum, pageSize, {
        shop_id,
        query,
        timeFrame: 'yearly',
        startDate: '',
        endDate: ''
      });

      if (response?.data) {
        const newOptions = Array.isArray(response.data) ? response.data : [];

        if (reset || pageNum == 1) {
          setOptions(newOptions);
        } else {
          setOptions(prev => [...prev, ...newOptions]);
        }

        setHasMore(newOptions.length == pageSize);
        setPage(pageNum);
      } else {
        // No data returned
        if (reset || pageNum == 1) {
          setOptions([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading options:', error);
      toaster.create({
        title: 'Error',
        description: 'Failed to load options',
        type: 'error'
      });
      // On error, don't append to existing options
      if (reset || pageNum == 1) {
        setOptions([]);
      }
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [endpoint, shop_id, loading, pageSize]);

  // Initial load
  useEffect(() => {
    if (isOpen && options.length == 0) {
      loadOptions(1, searchQuery, true);
    }
  }, [isOpen, loadOptions, searchQuery]);

  // Handle scroll
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollHeight - scrollTop <= clientHeight + 5 && hasMore && !loading) {
      loadOptions(page + 1, searchQuery, false);
    }
  }, [hasMore, loading, page, searchQuery, loadOptions]);

  // Handle search
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(1);
    loadOptions(1, query, true);
  }, [loadOptions]);

  // Handle selection
  const handleSelect = (option: any) => {
    setSelectedOption(option);
    onChange(option[valueField]);
    setIsOpen(false);
  };

  // Find selected option display text
  const getDisplayText = () => {
    if (selectedOption) {
      return displayField == 'supplier_name_with_branch' 
        ? `${selectedOption.supplier_name}${selectedOption.supplier_branch ? ` (${selectedOption.supplier_branch})` : ''}`
        : selectedOption[displayField];
    }
    if (value && options.length > 0) {
      const found = options.find(opt => opt[valueField] == value);
      if (found) {
        setSelectedOption(found);
        return displayField == 'supplier_name_with_branch' 
          ? `${found.supplier_name}${found.supplier_branch ? ` (${found.supplier_branch})` : ''}`
          : found[displayField];
      }
    }
    return placeholder;
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <Box position="relative" ref={dropdownRef}>
      <Box
        as="button"
        w="19rem"
        px={3}
        py={2}
        bg={bg}
        color={color}
        borderWidth="1px"
        borderColor={borderColor}
        borderRadius="md"
        textAlign="left"
        cursor="pointer"
        _hover={_hover}
        _focus={_focus}
        onClick={() => setIsOpen(!isOpen)}
        display="flex"
        justifyContent="space-between"
        alignItems="center"
      >
        <Text truncate>{getDisplayText()}</Text>
        <Text transform={isOpen ? 'rotate(180deg)' : 'rotate(0deg)'} transition="transform 0.2s">
          ▼
        </Text>
      </Box>

      {isOpen && (
        <Box
          position="absolute"
          top="100%"
          left={0}
          right={0}
          zIndex={1000}
          bg={bg}
          borderWidth="1px"
          borderColor="gray.200"
          borderRadius="md"
          boxShadow="lg"
          maxH="300px"
          overflow="hidden"
        >
          {/* Search input */}
          <Box p={2} borderBottom="1px solid" borderColor="gray.100">
            <Input
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              size="sm"
            />
          </Box>

          {/* Options list */}
          <Box
            ref={listRef}
            maxH="250px"
            overflowY="auto"
            px={3}
            onScroll={handleScroll}
          >
            {options.map((option, index) => (
              <Box
                key={`${option[valueField]}-${index}`}
                px={1}
                py={2}
                cursor="pointer"
                _hover={{ bg: 'cyan.150' }}
                bg={value == option[valueField] ? 'blue.50' : 'transparent'}
                onClick={() => handleSelect(option)}
                borderBottom="1px solid"
                borderColor="gray.50"
              >
                <Text truncate textStyle="xs">{
                  displayField == 'supplier_name_with_branch' 
                    ? `${option.supplier_name}${option.supplier_branch ? ` (${option.supplier_branch})` : ''}`.toLowerCase()
                    : option[displayField]?.toLowerCase()
                }</Text>
              </Box>
            ))}

            {loading && (
              <Box p={3} textAlign="center">
                <Spinner size="sm" color="blue.500" />
                <Text fontSize="sm" color="gray.500" mt={1}>Loading...</Text>
              </Box>
            )}

            {!loading && !hasMore && options.length > 0 && (
              <Box p={2} textAlign="center">
                <Text fontSize="sm" color="gray.500">No more options</Text>
              </Box>
            )}

            {!loading && options.length == 0 && (
              <Box p={3} textAlign="center">
                <Text fontSize="sm" color="gray.500">No options found</Text>
              </Box>
            )}
          </Box>
        </Box>
      )}
    </Box>
  );
};
interface SelectedItem {
  trans_id: any;
  receipt_id: string;
  receipt_item_id: string;
  item_name: string;
  quantity: number;
  unit_price: number;
  total: number;
  max_quantity: number;
}

interface CustomOrderItem {
  id: string;
  item_name: string;
  quantity: number;
  unit_price: number;
  total: number;
}
// Form validation schemas
const supplierSchema = z.object({
  supplier_name: z.string().min(1, 'Supplier name is required'),
  supplier_branch: z.string().optional(),
  supplier_contact: z.string().min(1, 'Contact information is required'),
  supplier_email: z.string().email('Invalid email address'),
  supplier_address: z.string().optional(),
});

const supplyItemSchema = z.object({
  supplier_id: z.string().min(1, 'Supplier is required'),
  item_name: z.string().min(1, 'Item name is required'),
  quantity: z.number().min(0, 'Quantity must be a positive number'),
  amount_expected: z.number().min(0, 'Amount must be a positive number'),
  amount_paid: z.number().min(0, 'Amount must be a positive number'),
  is_custom_order: z.boolean().optional(),
  attachments: z.array(z.string()).optional(),
});

const supplySaleSchema = z.object({
  supply_id: z.string().min(1, 'Supply item is required'),
  quantity_sold: z.number().min(1, 'Quantity must be at least 1'),
  selling_price: z.number().min(0, 'Selling price must be a positive number'),
  customer_name: z.string().optional(),
  customer_contact: z.string().optional(),
  payment_method: z.string().optional(),
  payment_status: z.string(),
  is_supplier: z.boolean().optional(),
  buyer_supplier_id: z.string().optional(),
});

const SuppliersPage = () => {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;
  const username = user?.username;

  // Determine current shop from Redux
  const currentShop = useAppSelector(state => {
    const user = state.user.currentUser;
    return user?.shops?.find((s: any) => s.shop_id == user.currentshop) || null;
  });

  // State variables
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStates, setLoadingStates] = useState({
    suppliers: false,
    supplies: false,
    sales: false,
    reports: false
  });
  const [timeFrame, setTimeFrame] = useState('yearly');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [supplyItems, setSupplyItems] = useState<SupplyItem[]>([]);
  const [supplySales, setSupplySales] = useState<SupplySale[]>([]);
  const [query, setQuery] = useState('');
  const [pagination, setPagination] = useState({
    suppliers: { currentPage: 1, pageSize: 10, totalRecords: 0 },
    supplies: { currentPage: 1, pageSize: 10, totalRecords: 0 },
    sales: { currentPage: 1, pageSize: 10, totalRecords: 0 },
    buyers: { currentPage: 1, pageSize: 10, totalRecords: 0 }
  });
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [showSupplierForm, setShowSupplierForm] = useState(false);
  const [showSupplyForm, setShowSupplyForm] = useState(false);
  const [showSaleForm, setShowSaleForm] = useState(false);
  const [showInvoice, setShowInvoice] = useState(false);
  const [showSaleInvoice, setShowSaleInvoice] = useState(false);
  const [selectedSupply, setSelectedSupply] = useState<SupplyItem | null>(null);
  const [selectedSale, setSelectedSale] = useState<SupplySale | null>(null);
  const [acceptedFiles, setAcceptedFiles] = useState([]);
  const [imageUrls, setImageUrls] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [showAttachments, setShowAttachments] = useState(false);
  const [selectedAttachments, setSelectedAttachments] = useState([]);
  const [vatRate, setVatRate] = useState(16);
  const [includeVat, setIncludeVat] = useState(false);
  const [activeTab, setActiveTab] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('suppliers-active-tab') || 'suppliers';
    }
    return 'suppliers';
  });
  const [expandedBuyers, setExpandedBuyers] = useState<Record<string, boolean>>({});
  const [isBuyerSupplier, setIsBuyerSupplier] = useState(false);

  // Sale payment modal state
  const [showSalePaymentModal, setShowSalePaymentModal] = useState(false);
  const [salePaymentAmount, setSalePaymentAmount] = useState(0);
  const [selectedSaleForPayment, setSelectedSaleForPayment] = useState<SupplySale | null>(null);
  const [salePaymentFiles, setSalePaymentFiles] = useState<File[]>([]);
  const [salePaymentHistory, setSalePaymentHistory] = useState<PaymentRecord[]>([]);

  // Sale edit modal state
  const [showEditSaleModal, setShowEditSaleModal] = useState(false);
  const [editSaleData, setEditSaleData] = useState<SupplySale | null>(null);

  // Supply payment request modal state
  const [showSupplyPaymentModal, setShowSupplyPaymentModal] = useState(false);
  const [supplyPaymentAmount, setSupplyPaymentAmount] = useState(0);
  const [selectedSupplyForPayment, setSelectedSupplyForPayment] = useState<SupplyItem | null>(null);
  const [supplyPaymentFiles, setSupplyPaymentFiles] = useState<File[]>([]);
  const [supplyPaymentHistory, setSupplyPaymentHistory] = useState<PaymentRecord[]>([]);
  const [selectedItems, setSelectedItems] = useState<Record<string, SelectedItem>>({});
  const [totalAmount, setTotalAmount] = useState(0);
  const [quantityToSell, setQuantityToSell] = useState(0);

  const calculateVat = (amount: number) => {
    return includeVat ? (amount * (vatRate / 100)) : 0;
  };


  // Form data
  const [supplierFormData, setSupplierFormData] = useState({
    supplier_name: '',
    supplier_branch: '',
    supplier_contact: '',
    supplier_email: '',
    supplier_address: '',
  });

  const [supplyFormData, setSupplyFormData] = useState({
    supplier_id: '',
    destination_id: '', // Where supply is going to
    item_name: '',
    quantity: 0,
    amount_expected: 0,
    amount_paid: 0,
    supply_date: new Date().toISOString().split('T')[0], // Default to today's date
    attachments: [],
    is_custom_order: true, // Default to custom order
  });

  // State for custom order items
  const [customOrderItems, setCustomOrderItems] = useState<CustomOrderItem[]>([]);
  const [newCustomItem, setNewCustomItem] = useState({
    item_name: '',
    quantity: 1,
    unit_price: 0
  });

  // State for inventory selection
  const [inventoryItems, setInventoryItems] = useState<Product[]>([]);
  const [selectedInventoryItems, setSelectedInventoryItems] = useState<{
    item_id: string;
    item_name: string;
    item_selling: any;
    quantity: number;
    max_quantity: number;
  }[]>([]);
  const [inventorySearchQuery, setInventorySearchQuery] = useState('');
  const [isLoadingInventory, setIsLoadingInventory] = useState(false);

  const [saleFormData, setSaleFormData] = useState({
    supply_id: '',
    quantity_sold: 0,
    selling_price: 0,
    customer_name: '',
    customer_contact: '',
    destination_id: '',
    payment_method: '',
    payment_status: '',
    is_supplier: true,
    buyer_supplier_id: '',
    attachments: [] as File[]
  });

  // Stats for dashboard
  const [totalSuppliers, setTotalSuppliers] = useState(0);
  const [totalSupplies, setTotalSupplies] = useState(0);
  const [pendingPayments, setPendingPayments] = useState(0);
  const [totalSales, setTotalSales] = useState(0);
  const [recentSupplies, setRecentSupplies] = useState<SupplyItem[]>([]);
  const [recentSales, setRecentSales] = useState<SupplySale[]>([]);
  const [showSupplierDetails, setShowSupplierDetails] = useState(false);

  // Enhanced reports data
  const [reportsData, setReportsData] = useState<any>(null);
  const [bestSellingItems, setBestSellingItems] = useState<any[]>([]);
  const [financialInsights, setFinancialInsights] = useState<any>(null);
  const [summaryStats, setSummaryStats] = useState<any>(null);

  // Load reports data (with time filtering)
  const loadReportsData = useCallback(async () => {
    if (!shop_id) return;
    
    setLoadingStates(prev => ({ ...prev, reports: true }));
    try {
      const dateParams = timeFrame && startDate && endDate ? { timeFrame, startDate, endDate } : {};
      const reportsResponse = await fetchData('reports', 1, 50, {
        shop_id,
        ...dateParams
      });

      if (reportsResponse?.data) {
        setReportsData(reportsResponse.data);
        setBestSellingItems(reportsResponse.data.bestSellingItems || []);
        setFinancialInsights(reportsResponse.data.financialInsights || {});
        setSummaryStats(reportsResponse.data.summaryStats || {});
      }
    } catch (error) {
      toaster.error({
        title: 'Failed to load reports',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setLoadingStates(prev => ({ ...prev, reports: false }));
    }
  }, [shop_id, timeFrame, startDate, endDate]);

  // Load suppliers data (with pagination and search)
  const loadSuppliersData = useCallback(async () => {
    if (!shop_id) return;
    
    setLoadingStates(prev => ({ ...prev, suppliers: true }));
    try {
      const { currentPage, pageSize } = pagination.suppliers;
      const suppliersResponse = await fetchData('suppliers', currentPage, pageSize, {
        shop_id,
        query
      });

      if (suppliersResponse?.data) {
        setSuppliers(suppliersResponse.data);
        setPagination(prev => ({
          ...prev,
          suppliers: { ...prev.suppliers, totalRecords: suppliersResponse.metadata?.total || 0 }
        }));
        setTotalSuppliers(suppliersResponse.metadata?.total);
      }
    } catch (error) {
      toaster.error({
        title: 'Failed to load suppliers',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setLoadingStates(prev => ({ ...prev, suppliers: false }));
    }
  }, [shop_id, pagination.suppliers.currentPage, pagination.suppliers.pageSize, query]);

  // Load supply items data (with pagination and search)
  const loadSupplyItemsData = useCallback(async () => {
    if (!shop_id) return;
    
    setLoadingStates(prev => ({ ...prev, supplies: true }));
    try {
      const { currentPage, pageSize } = pagination.supplies;
      const suppliesResponse = await fetchData<SupplyItem>('supply_items', currentPage, pageSize, {
        shop_id,
        query
      });

      if (suppliesResponse?.data) {
        const enriched = suppliesResponse.data.map(item => {
          const due = item.amount_expected - item.amount_paid;
          const status = due == 0 ? 'paid' : item.amount_paid > 0 ? 'partial' : 'pending';
          return { ...item, amount_due: due, payment_status: status };
        });

        setSupplyItems(enriched);
        setPagination(prev => ({
          ...prev,
          supplies: { ...prev.supplies, totalRecords: suppliesResponse.metadata?.total || 0 }
        }));
        setTotalSupplies(enriched.reduce((sum, item) => sum + (item.quantity || 0), 0));
        setPendingPayments(enriched.reduce((sum, item) => sum + (item.amount_expected - item.amount_paid), 0));
        setRecentSupplies(enriched.slice(0, 5));
      }
    } catch (error) {
      toaster.error({
        title: 'Failed to load supply items',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setLoadingStates(prev => ({ ...prev, supplies: false }));
    }
  }, [shop_id, pagination.supplies.currentPage, pagination.supplies.pageSize, query]);

  // Load supply sales data (with pagination and search)
  const loadSupplySalesData = useCallback(async () => {
    if (!shop_id) return;
    
    setLoadingStates(prev => ({ ...prev, sales: true }));
    try {
      const { currentPage, pageSize } = pagination.sales;
      const salesResponse = await fetchData<SupplySale>('supply_sales', currentPage, pageSize, {
        shop_id,
        query
      });

      if (salesResponse?.data) {
        setSupplySales(salesResponse.data);
        setPagination(prev => ({
          ...prev,
          sales: { ...prev.sales, totalRecords: salesResponse.metadata?.total || 0 }
        }));
        setTotalSales(salesResponse.metadata.total);
        setRecentSales(salesResponse.data.slice(0, 5));
      }
    } catch (error) {
      toaster.error({
        title: 'Failed to load supply sales',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setLoadingStates(prev => ({ ...prev, sales: false }));
    }
  }, [shop_id, pagination.sales.currentPage, pagination.sales.pageSize, query]);

  // Load data based on active tab
  const loadTabData = useCallback(() => {
    switch (activeTab) {
      case 'suppliers':
        loadSuppliersData();
        break;
      case 'supplies':
        loadSupplyItemsData();
        break;
      case 'sales':
        loadSupplySalesData();
        break;
      case 'reports':
        loadReportsData();
        break;
      default:
        break;
    }
  }, [activeTab, loadSuppliersData, loadSupplyItemsData, loadSupplySalesData, loadReportsData]);

  //       if (suppliersResponse?.data) {
  //         setSuppliers(suppliersResponse?.data);
  //         setTotalRecords(suppliersResponse?.metadata?.total);
  //         setTotalSuppliers(suppliersResponse?.metadata?.total);
  //       }

  //       // Fetch supply items
  //       const supplyParams = timeFrame && startDate && endDate
  //         ? { shop_id, query, timeFrame, startDate, endDate }
  //         : { shop_id, query };
  //       const suppliesResponse = await fetchData<SupplyItem>('supply_items', currentPage, pageSize, supplyParams);

  //       if (suppliesResponse?.data) {
  //         const enriched = suppliesResponse?.data?.map(item => {

  //           const due = item.amount_expected - item.amount_paid;
  //           const status = due == 0 ? 'paid' : item.amount_paid > 0 ? 'partial' : 'pending';
  //           return { ...item, amount_due: due, payment_status: status };

  //         });
  //         setSupplyItems(enriched);

  //         const totalQuantity = enriched.reduce((sum, item) => sum + (item.quantity || 0), 0);
  //         setTotalSupplies(totalQuantity);

  //         const pending = enriched.reduce((total, item) => {
  //           return total + (item?.amount_expected - item?.amount_paid);
  //         }, 0);

  //         setPendingPayments(pending);

  //         setRecentSupplies(suppliesResponse?.data?.slice(0, 5));
  //       }

  //       // Fetch supply sales
  //       const salesParams = timeFrame && startDate && endDate
  //         ? { shop_id, query, timeFrame, startDate, endDate }
  //         : { shop_id, query };
  //       const salesResponse = await fetchData<SupplySale>('supply_sales', currentPage, pageSize, salesParams);

  //       if (salesResponse?.data) {

  //         setSupplySales(salesResponse.data);
  //         setTotalSales(salesResponse.metadata.total);
  //         setRecentSales(salesResponse.data.slice(0, 5));
  //       }
  //     } catch (error) {
  //       toaster.error({
  //         title: 'Failed to load data',
  //         description: error instanceof Error ? error.message : 'An unexpected error occurred',
  //       });
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   }, [shop_id, timeFrame, currentPage, pageSize, query, startDate, endDate]);


  
  // Load data when tab changes or relevant dependencies change
  useEffect(() => {
    loadTabData();
  }, [loadTabData]);

  // Load reports when time frame changes (only for reports tab)
  useEffect(() => {
    if (activeTab == 'reports' && timeFrame !== 'custom') {
      loadReportsData();
    }
  }, [timeFrame, startDate, endDate, activeTab, loadReportsData]);

  // Handle tab change
  const handleTabChange = (details: { value: string }) => {
    setActiveTab(details.value);
    localStorage.setItem('suppliers-active-tab', details.value);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPagination(prev => ({
      ...prev,
      [activeTab]: { ...prev[activeTab], currentPage: newPage.page }
    }));
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize) => {
    setPagination(prev => ({
      ...prev,
      [activeTab]: { ...prev[activeTab], pageSize: newPageSize.pagesize, currentPage: 1 }
    }));
  };

  // Handle search
  const handleSearch = (searchValue) => {
    setQuery(searchValue);
    setPagination(prev => ({
      ...prev,
      [activeTab]: { ...prev[activeTab], currentPage: 1 }
    }));
  };


  const handleViewAttachments = (item) => {
    if (item?.attachments?.length > 0) {
      setSelectedAttachments(item?.attachments);
      setShowAttachments(true);
    } else {
      toaster.create({
        title: 'No Attachments',
        description: 'This supply item has no attachments.',
      });
    }
  };



  const handleTimeFrameChange = (event) => {
    const newTimeFrame = event.target.value;
    setTimeFrame(newTimeFrame);
    setPagination(prev => ({
      ...prev,
      [activeTab]: { ...prev[activeTab], currentPage: 1 }
    }));
    // custom: show pickers only
    if (newTimeFrame == 'custom') {
      setStartDate('');
      setEndDate('');
      return;
    }
    // compute start/end for other frames
    const today = new Date();
    let startDateObj;
    switch (newTimeFrame) {
      case 'daily': startDateObj = today; break;
      case 'weekly': startDateObj = new Date(today); startDateObj.setDate(today.getDate() - 6); break;
      case 'monthly': startDateObj = new Date(today.getFullYear(), today.getMonth(), 1); break;
      case 'yearly': startDateObj = new Date(today.getFullYear(), 0, 1); break;
      default: startDateObj = today;
    }
    const formatDate = d => d.toISOString().split('T')[0];
    setStartDate(formatDate(startDateObj));
    setEndDate(formatDate(today));
  };


  // Handle supplier form input changes
  const handleSupplierFormChange = (e) => {
    const { name, value } = e.target;
    setSupplierFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle supply form input changes
  const handleSupplyFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (type == 'checkbox') {
      setSupplyFormData(prev => ({
        ...prev,
        [name]: checked
      }));

      // If switching to inventory mode, fetch inventory items
      if (name == 'is_custom_order' && !checked) {
        fetchInventoryItems();
      }
    } else {
      setSupplyFormData(prev => ({
        ...prev,
        [name]: name == 'quantity' || name == 'amount_expected' || name == 'amount_paid'
          ? parseFloat(value)
          : value
      }));

    }
  };

  // Handle new custom item input changes
  const handleNewCustomItemChange = (e) => {
    const { name, value } = e.target;
    setNewCustomItem(prev => ({
      ...prev,
      [name]: name == 'quantity' || name == 'unit_price' ? parseFloat(value) || 0 : value
    }));
  };

  // Add new custom order item
  const addCustomOrderItem = () => {
    if (!newCustomItem.item_name.trim()) {
      toaster.create({
        title: 'Error',
        description: 'Item name is required',
        type: 'error'
      });
      return;
    }

    if (newCustomItem.quantity <= 0) {
      toaster.create({
        title: 'Error',
        description: 'Quantity must be greater than 0',
        type: 'error'
      });
      return;
    }

    if (newCustomItem.unit_price <= 0) {
      toaster.create({
        title: 'Error',
        description: 'Unit price must be greater than 0',
        type: 'error'
      });
      return;
    }

    const newItem: CustomOrderItem = {
      id: Date.now().toString(),
      item_name: newCustomItem.item_name.trim(),
      quantity: newCustomItem.quantity,
      unit_price: newCustomItem.unit_price,
      total: newCustomItem.quantity * newCustomItem.unit_price
    };

    setCustomOrderItems(prev => [...prev, newItem]);
    setNewCustomItem({
      item_name: '',
      quantity: 1,
      unit_price: 0
    });
  };

  // Remove custom order item
  const removeCustomOrderItem = (id: string) => {
    setCustomOrderItems(prev => prev.filter(item => item.id !== id));
  };

  // Update custom order item
  const updateCustomOrderItem = (id: string, field: string, value: any) => {
    setCustomOrderItems(prev => prev.map(item => {
      if (item.id == id) {
        const updatedItem = { ...item, [field]: value };
        if (field == 'quantity' || field == 'unit_price') {
          updatedItem.total = updatedItem.quantity * updatedItem.unit_price;
        }
        return updatedItem;
      }
      return item;
    }));
  };

  // Fetch inventory items
  const fetchInventoryItems = async () => {
    setIsLoadingInventory(true);
    try {
      const response = await fetchData<Product>('items', 1, 100, {
        shop_id,
        query: inventorySearchQuery
      });

      if (response?.data) {
        setInventoryItems(response.data as Product[]);
      }
    } catch (error) {
      toaster.error({
        title: 'Error',
        description: 'Failed to fetch inventory items'
      });
    } finally {
      setIsLoadingInventory(false);
    }
  };

  // Handle inventory search
  const handleInventorySearch = (query) => {
    setInventorySearchQuery(query);
    fetchInventoryItems();
  };

  // Handle inventory item selection
  const handleInventoryItemSelect = (item, isSelected) => {
    // Chakra UI Checkbox passes the checked state directly
    if (isSelected) {
      // Add to selected items if not already selected
      if (!selectedInventoryItems.some(i => i.item_id == item.item_id)) {
        setSelectedInventoryItems(prev => [
          ...prev,
          {
            item_id: item.item_id,
            item_name: item.item_name,
            item_selling: item.item_selling,
            quantity: 1,
            max_quantity: item.item_quantity
          }
        ]);
      }
    } else {
      // Remove from selected items
      setSelectedInventoryItems(prev =>
        prev.filter(i => i.item_id !== item.item_id)
      );
    }
  };

  // Handle quantity change for selected inventory item
  const handleInventoryItemQuantityChange = (itemId, quantity) => {
    setSelectedInventoryItems(prev =>
      prev.map(item =>
        item.item_id == itemId
          ? { ...item, quantity: Math.min(quantity, item.max_quantity) }
          : item
      )
    );
  };

  // Handle select all inventory items
  const handleSelectAllInventoryItems = (isSelected) => {
    // Chakra UI Checkbox passes the checked state directly
    if (isSelected) {
      const newSelectedItems = inventoryItems.map(item => ({
        item_id: item.item_id,
        item_name: item.item_name,
        quantity: 1,
        max_quantity: item.item_quantity
      }));
      setSelectedInventoryItems(newSelectedItems);
    } else {
      setSelectedInventoryItems([]);
    }
  };

  // Submit supplier form
  const handleSupplierSubmit = async () => {
    try {
      setIsLoading(true);
      const validatedData = supplierSchema.parse(supplierFormData);

      const supplierData = {
        ...validatedData,

        shop_id,
        supplier_status: 'active'
      };

      if (isEditing && selectedSupplier) {
        await updateData('suppliers', selectedSupplier.supplier_id, supplierData);
        toaster.success({ title: 'Supplier updated successfully' });
      } else {
        await insertData('suppliers', supplierData);
        toaster.success({ title: 'Supplier added successfully' });
      }

      resetSupplierForm();
      loadSuppliersData();
    } catch (error) {
      toaster.error({
        title: isEditing ? 'Failed to update supplier' : 'Failed to add supplier',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetSupplierForm = () => {
    setSupplierFormData({
      supplier_name: '',
      supplier_branch: '',
      supplier_contact: '',
      supplier_email: '',
      supplier_address: '',
    });
    setIsEditing(false);
    setSelectedSupplier(null);
    setShowSupplierForm(false);
  };

  // Submit supply form
  const handleSupplySubmit = async () => {
    try {
      setIsLoading(true);
      let uploadedFiles = [];

    if (acceptedFiles.length > 0) {
        const formData = new FormData();
        acceptedFiles.forEach(file => {
          formData.append('item_pic_url', file);
        });
        const response = await insertData("upload_pic", formData);
        if (!response?.files || !Array.isArray(response.files)) {
          throw new Error("Invalid response format");
        }
        uploadedFiles = response.files;
      }

      let supplyData;

      if (supplyFormData.is_custom_order) {
        if (customOrderItems.length == 0) {
          throw new Error('Please add at least one item to the custom order');
        }

        const totalQuantity = customOrderItems.reduce((sum, item) => sum + item.quantity, 0);
        const totalAmount = customOrderItems.reduce((sum, item) => sum + item.total, 0);

        // Ensure suppliers are loaded
        if (suppliers.length == 0) {
          await loadSuppliersData();
        }
        
        const supplierName = suppliers.find(s => s.supplier_id == supplyFormData.destination_id)?.supplier_name || 'Unknown Supplier';  
        const validatedData = supplyItemSchema.parse({
          ...supplyFormData,
          quantity: totalQuantity,
          amount_expected: totalAmount,
          item_name: customOrderItems.length == 1
            ? customOrderItems[0].item_name
            : `${supplierName} Order (${customOrderItems.length} items)`,
          attachments: uploadedFiles,
          created_by: username,
        });
        // ${supplyFormData.} 
         supplyData = {
          ...validatedData,
          supplier_id: supplyFormData.supplier_id,
          destination_id: supplyFormData.destination_id,
          shop_id,
          created_by: username,
          attachments: uploadedFiles,
          supply_date: supplyFormData.supply_date,
          custom_order_items: customOrderItems,
          is_custom_order: true
        };

      } else {
        if (selectedInventoryItems.length == 0) {

          throw new Error('Please select at least one inventory item');
        }

         const supplierName = suppliers.find(s => s.supplier_id == supplyFormData.destination_id)?.supplier_name || 'Unknown Supplier';
         
         supplyData = {
              supplier_id: supplyFormData.supplier_id,
              item_name: `${supplierName} supplies`,
              quantity: selectedInventoryItems.reduce((total, item) => total + (item.quantity || 0), 0),
              destination_id: supplyFormData.destination_id,
              selected_items:selectedInventoryItems,
              amount_expected: supplyFormData.amount_expected,
              amount_paid: supplyFormData.amount_paid,
              shop_id,
              created_by:username,
              attachments: uploadedFiles,
              supply_date: supplyFormData.supply_date

          };



      }

      const result = await insertData('supply_items', supplyData);
      if (result) {
        toaster.success({
          title: 'Success',
          description: `Supply items added successfully`
        });
      } else {
        throw new Error('Failed to add some supply items');
      }

      setSupplyFormData({
        supplier_id: '',
        destination_id: '',
        item_name: '',
        quantity: 0,
        amount_expected: 0,
        amount_paid: 0,
        supply_date: new Date().toISOString().split('T')[0],
        attachments: [],
        is_custom_order: true
      });
      setSelectedInventoryItems([]);
      setCustomOrderItems([]);
      setNewCustomItem({
        item_name: '',
        quantity: 1,
        unit_price: 0
      });
      setAcceptedFiles([]);
      setShowSupplyForm(false);
      loadSupplyItemsData();


    } catch (error) {
      toaster.error({
        title: 'Failed to add supply item',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Export report function
  const exportReport = async (tabType, format) => {
    try {
      setIsLoading(true);
      
      const exportParams = {
        shop_id,
        timeFrame,
        startDate,
        endDate,
        query: query || '',
        format,
        export: true
      };
      
      const endpoint = {
        suppliers: 'suppliers',
        supplies: 'supply_items', 
        sales: 'supply_sales',
        buyers: 'supply_sales'
      }[tabType];
      
      const response = await fetchData(endpoint, 1, 10000, exportParams);
      
      console.log('Response:', response);

      if (format == 'csv') {
        downloadCSV(response.data, tabType);
      } else {
        downloadPDF(response.data, tabType);
      }
      
      toaster.success({ title: `${format.toUpperCase()} exported successfully` });
    } catch (error) {
      toaster.error({ 
        title: 'Export failed', 
        description: error.message 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const downloadCSV = (data, tabType) => {
    const flattenData = (obj, prefix = '') => {
      let result = {};
      for (let key in obj) {
        if (obj[key] === null || obj[key] === undefined) {
          result[prefix + key] = '';
        } else if (Array.isArray(obj[key])) {
          if (obj[key].length === 0) {
            result[prefix + key] = '';
          } else {
            obj[key].forEach((item, index) => {
              if (typeof item === 'object') {
                Object.assign(result, flattenData(item, `${prefix}${key}_${index + 1}_`));
              } else {
                result[`${prefix}${key}_${index + 1}`] = item;
              }
            });
          }
        } else if (typeof obj[key] === 'object') {
          Object.assign(result, flattenData(obj[key], `${prefix}${key}_`));
        } else {
          result[prefix + key] = obj[key];
        }
      }
      return result;
    };

    const flattenedData = data.map(row => flattenData(row));
    const allHeaders = [...new Set(flattenedData.flatMap(row => Object.keys(row)))];
    
    const csvContent = [
      allHeaders.join(','),
      ...flattenedData.map(row => 
        allHeaders.map(header => `"${(row[header] || '').toString().replace(/"/g, '""')}"`).join(',')
      )
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${tabType}_comprehensive_report_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };
  
  const downloadPDF = (data, tabType) => {
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    let yPosition = 25;
    
    // Modern header with gradient effect
    pdf.setFillColor(41, 128, 185);
    pdf.rect(0, 0, pageWidth, 35, 'F');
    
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(20);
    pdf.setFont('helvetica', 'bold');
    pdf.text(`${tabType.toUpperCase()} COMPREHENSIVE REPORT`, 20, 20);
    
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.text(`Generated: ${new Date().toLocaleDateString()} | Total Records: ${data.length}`, 20, 30);
    
    yPosition = 50;
    pdf.setTextColor(0, 0, 0);
    
    data.forEach((item, index) => {
      if (yPosition > pageHeight - 40) {
        pdf.addPage();
        yPosition = 20;
      }
      
      // Item header with background
      pdf.setFillColor(236, 240, 241);
      pdf.rect(15, yPosition - 5, pageWidth - 30, 12, 'F');
      
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(52, 73, 94);
      
      const title = {
        suppliers: `Supplier: ${item.supplier_name || 'N/A'}`,
        supplies: `Supply: ${item.item_name || 'N/A'}`,
        sales: `Sale #${item.invoce_no || 'N/A'} - ${item.customer_name || 'N/A'}`,
        buyers: `Buyer: ${item.customer_name || 'N/A'}`
      }[tabType] || `Record ${index + 1}`;
      
      pdf.text(title, 20, yPosition);
      yPosition += 15;
      
      // Main details
      pdf.setFontSize(9);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(0, 0, 0);
      
      const renderObject = (obj, indent = 0, skipKeys = []) => {
        Object.entries(obj).forEach(([key, value]) => {
          if (skipKeys.includes(key) || value === null || value === undefined) return;
          
          if (yPosition > pageHeight - 20) {
            pdf.addPage();
            yPosition = 20;
          }
          
          const xPos = 20 + (indent * 10);
          
          if (Array.isArray(value)) {
            if (value.length > 0) {
              pdf.setFont('helvetica', 'bold');
              pdf.text(`${key.replace(/_/g, ' ').toUpperCase()}:`, xPos, yPosition);
              yPosition += 5;
              
              value.forEach((arrItem, arrIndex) => {
                if (typeof arrItem === 'object') {
                  pdf.setFont('helvetica', 'italic');
                  pdf.text(`  Item ${arrIndex + 1}:`, xPos + 5, yPosition);
                  yPosition += 4;
                  renderObject(arrItem, indent + 2);
                } else {
                  pdf.setFont('helvetica', 'normal');
                  pdf.text(`  - ${arrItem}`, xPos + 5, yPosition);
                  yPosition += 4;
                }
              });
            }
          } else if (typeof value === 'object') {
            pdf.setFont('helvetica', 'bold');
            pdf.text(`${key.replace(/_/g, ' ').toUpperCase()}:`, xPos, yPosition);
            yPosition += 5;
            renderObject(value, indent + 1);
          } else {
            pdf.setFont('helvetica', 'bold');
            pdf.text(`${key.replace(/_/g, ' ')}:`, xPos, yPosition);
            pdf.setFont('helvetica', 'normal');
            const textValue = value.toString().substring(0, 50);
            pdf.text(textValue, xPos + 40, yPosition);
            yPosition += 5;
          }
        });
      };
      
      renderObject(item);
      yPosition += 10;
      
      // Separator line
      pdf.setDrawColor(189, 195, 199);
      pdf.line(20, yPosition - 5, pageWidth - 20, yPosition - 5);
      yPosition += 5;
    });
    
    // Footer
    const totalPages = pdf.internal.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setTextColor(127, 140, 141);
      pdf.text(`Page ${i} of ${totalPages}`, pageWidth - 30, pageHeight - 10);
    }
    
    pdf.save(`${tabType}_comprehensive_report_${new Date().toISOString().split('T')[0]}.pdf`);
  };
  
  
  // const downloadCSV = (data, tabType) => {
  //   const headers = {
  //     suppliers: ['Supplier Name', 'Branch', 'Contact', 'Email', 'Address'],
  //     supplies: ['Item Name', 'Supplier', 'Quantity', 'Amount Expected', 'Amount Paid', 'Date'],
  //     sales: ['Customer', 'Item', 'Quantity', 'Total Amount', 'Date', 'Status'],
  //     buyers: ['Customer', 'Item', 'Quantity', 'Total Amount', 'Date', 'Status']
  //   }[tabType];
    
  //   const csvContent = [headers.join(','), ...data.map(row => {
  //     const values = {
  //       suppliers: [row.supplier_name, row.supplier_branch, row.supplier_contact, row.supplier_email, row.supplier_address],
  //       supplies: [row.item_name, row.supplier_name, row.quantity, row.amount_expected, row.amount_paid, row.supply_date],
  //       sales: [row.customer_name, row.item_name, row.quantity_sold, row.total_amount, row.sale_date, row.payment_status],
  //       buyers: [row.customer_name, row.item_name, row.quantity_sold, row.total_amount, row.sale_date, row.payment_status]
  //     }[tabType];
  //     return values.map(v => `"${v || ''}"`).join(',');
  //   })].join('\n');
    
  //   const blob = new Blob([csvContent], { type: 'text/csv' });
  //   const url = URL.createObjectURL(blob);
  //   const a = document.createElement('a');
  //   a.href = url;
  //   a.download = `${tabType}_report_${new Date().toISOString().split('T')[0]}.csv`;
  //   a.click();
  //   URL.revokeObjectURL(url);
  // };
  
  // const downloadPDF = (data, tabType) => {
  //   // Basic PDF generation - you might want to use a library like jsPDF
  //   const content = data.map(row => {
  //     const values = {
  //       suppliers: `${row.supplier_name} | ${row.supplier_branch} | ${row.supplier_contact}`,
  //       supplies: `${row.item_name} | ${row.supplier_name} | ${row.quantity} | ${row.amount_expected}`,
  //       sales: `${row.customer_name} | ${row.item_name} | ${row.quantity_sold} | ${row.total_amount}`,
  //       buyers: `${row.customer_name} | ${row.item_name} | ${row.quantity_sold} | ${row.total_amount}`
  //     }[tabType];
  //     return values;
  //   }).join('\n');
    
  //   const blob = new Blob([content], { type: 'text/plain' });
  //   const url = URL.createObjectURL(blob);
  //   const a = document.createElement('a');
  //   a.href = url;
  //   a.download = `${tabType}_report_${new Date().toISOString().split('T')[0]}.txt`;
  //   a.click();
  //   URL.revokeObjectURL(url);
  // };

  // Submit sale form
  const handleSaleSubmit = async () => {
    try {
      setIsLoading(true);
      let saleData;
      const supplyItem = supplyItems.find(item => item.supply_id == saleFormData.supply_id);
      if (!supplyItem) {
        throw new Error('Supply item not found');
      }
      const availableQuantity = supplyItem.quantity - (supplyItem.quantity_sold || 0);
      if (saleFormData.quantity_sold > availableQuantity) {
        throw new Error(`Only ${availableQuantity} units available for sale`);
      }

      // const totalAmount = saleFormData.quantity_sold * saleFormData.selling_price;
      let uploadedFiles = [];

      if (acceptedFiles.length > 0) {
        const formData = new FormData();
        acceptedFiles.forEach(file => {
          formData.append('item_pic_url', file);
        });
        const response = await insertData("upload_pic", formData);
        if (!response?.files || !Array.isArray(response?.files)) {
          throw new Error("Invalid response format");
        }
        uploadedFiles = response?.files;
      }

      if (selectedSupply?.transaction) {
        if (Object.keys(selectedItems).length == 0) {
          throw new Error('Please select at least one item to sell');
        }

        const totalAmount = Object.values(selectedItems).reduce((sum, item) => sum + parseFloat(item.unit_price)*parseFloat(item.quantity), 0);
        const vatAmount = includeVat ? calculateVat(totalAmount) : 0;


        saleData = {
          ...saleFormData,
          shop_id,
          sale_date: new Date().toISOString(),
          total_amount: totalAmount,
          vat_included: includeVat,
          vat_rate: includeVat ? vatRate : 0,
          vat_amount: vatAmount,
          supplier_id: selectedSupply.supplier_id,
          item_name: selectedSupply.item_name,
          supply_branch: null,
          extra_data:  Object.values(selectedItems).map(item => ({
            receipt_id:item.receipt_id,
            receipt_item_id: item.receipt_item_id,
            quantity: item.quantity,
            item_name:item.item_name,
            unit_price: item.unit_price,
            trans_id:selectedSupply?.trans_id,
            total_amount:parseFloat((parseFloat(item.quantity) * parseFloat(item.unit_price)).toFixed(2))
          })),
          quantity_sold: Object.values(selectedItems).reduce(
            (sum, item) => sum + Number(item.quantity),
            0
          ),
          attachments: uploadedFiles,
          is_transaction: true,
          items: Object.values(selectedItems).map(item => ({
            receipt_id:item.receipt_id,
            receipt_item_id: item.receipt_item_id,
            quantity: item.quantity,
            unit_price: item.unit_price,
            trans_id:selectedSupply?.trans_id
          }))
        };

        
        if (selectedSupply?.destination) {
            saleData.customer_name = selectedSupply.destination.supplier_name;
            saleData.supply_branch = selectedSupply.destination.supplier_branch;
            saleData.customer_contact = selectedSupply.destination.supplier_contact;
            saleData.buyer_supplier_id = selectedSupply.destination.supplier_id;
        }


      } else {

        const totalAmount = saleFormData.quantity_sold * saleFormData.selling_price;
        const vatAmount = includeVat ? calculateVat(totalAmount) : 0;

        saleData = {
          ...saleFormData,
          shop_id,
          sale_date: new Date().toISOString(),
          total_amount: totalAmount,
          vat_included: includeVat,
          vat_rate: includeVat ? vatRate : 0,
          vat_amount: vatAmount,
          supplier_id: supplyItem.supplier_id,
          item_name: supplyItem.item_name,
          attachments: uploadedFiles,
          is_transaction: false,
          destination_id: saleFormData.buyer_supplier_id,
          supplier_branch: null
        };


        if (saleFormData.is_supplier && saleFormData.buyer_supplier_id) {
          const buyerSupplier = suppliers.find(s => s.supplier_id == saleFormData.buyer_supplier_id);
          
          if (buyerSupplier) {
            saleData.customer_name = buyerSupplier.supplier_name;
            saleData.supply_branch = buyerSupplier.supplier_branch;
            saleData.customer_contact = buyerSupplier.supplier_contact;
          }
        }
      }

     await updateData('supply_items_receipt', supplyItem.supply_id,saleData);

     const saleResponse = await insertData('supply_sales', saleData);

      setSaleFormData({
        supply_id: '',
        quantity_sold: 0,
        selling_price: 0,
        customer_name: '',
        customer_contact: '',
        destination_id: '',
        payment_method: '',
        payment_status: '',
        is_supplier: true,
        buyer_supplier_id: '',
        attachments: []
      });

      setShowSaleForm(false);
      setSelectedSupply(null);
      setSelectedItems({});
      setAcceptedFiles([]);
      setTotalAmount(0);
      setQuantityToSell(0);

      if (saleResponse && saleResponse?.sale_id) {
        const newSale = {
          ...saleData,
          sale_id: saleResponse?.sale_id,
          supply_id: supplyItem.supply_id
        };
        setSelectedSale(newSale);
        // setShowSaleInvoice(true);
      } 
      toaster.success({
        title: 'Supply sale recorded successfully',
        description: 'The supply sale has been added to your database.',
      });
    } catch (error) {
      toaster.error({
        title: 'Failed to record supply sale',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
      });
    } finally {
      const soldQuantity = selectedSupply?.transaction ? 
      Object.values(selectedItems).reduce((sum, selectedItem) => sum + Number(selectedItem.quantity), 0) : 
      saleFormData.quantity_sold;
  
      setSupplyItems(prev => prev.map(item => 
        item.supply_id == supplyItems.supply_id 
          ? { 
              ...item, 
              quantity_sold: (item.quantity_sold || 0) + soldQuantity,
              available: Math.max(0, (item.available || (item.quantity - (item.quantity_sold || 0))) - soldQuantity)
            } 
          : item
      ));

        setIsLoading(false);
    }
  };



  const handleGenerateReport = async (reportType = 'suppliers', format = 'csv') => {
    setIsLoading(true);

    try {
      if (reportType == 'sales' && selectedSale) {
        // Make sure modal is open
        if (!showSaleInvoice) {
          setShowSaleInvoice(true);
          // Wait for modal to be fully rendered
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        const invoiceContent = document.getElementById('invoice-content');
        if (!invoiceContent) {
          throw new Error('Could not find invoice content');
        }

        // Create a clone of the content to avoid affecting the original
        const contentClone = invoiceContent.cloneNode(true) as HTMLElement;

        // Hide any elements that shouldn't be in the PDF
        const elementsToHide = contentClone.querySelectorAll('button, [aria-hidden="true"]');
        elementsToHide.forEach(el => (el as HTMLElement).style.display = 'none');

        // Create a temporary container for the clone
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'fixed';
        tempContainer.style.left = '-9999px';
        tempContainer.appendChild(contentClone);
        document.body.appendChild(tempContainer);

        try {
          const canvas = await html2canvas(contentClone, {
            scale: 2,
            useCORS: true,
            logging: false,
            backgroundColor: '#ffffff',
            removeContainer: true
          });

          const imgData = canvas.toDataURL('image/png');
          const pdf = new jsPDF('p', 'mm', 'a4');
          const imgWidth = 210; // A4 width in mm
          const pageHeight = 295; // A4 height in mm
          const imgHeight = (canvas.height * imgWidth) / canvas.width;

          let heightLeft = imgHeight;
          let position = 0;

          // Add first page
          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;

          // Add new pages if content is longer than one page
          while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
          }

          pdf.save(`invoice-${selectedSale.sale_id}.pdf`);

          toaster.success({
            title: 'Invoice Downloaded',
            description: 'The invoice has been downloaded as PDF.',
          });
        } finally {
          // Clean up
          document.body.removeChild(tempContainer);
        }
        return;
      }

      // Rest of your existing code for other report types...
    } catch (error) {
      console.error('Error generating report:', error);
      toaster.error({
        title: 'Failed to generate report',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  };






  useEffect(() => {
    if (!supplyFormData.is_custom_order && selectedInventoryItems.length > 0) {
      const totalAmount = selectedInventoryItems.reduce((sum, item) => {
        return sum + (item.item_selling * item.quantity);
      }, 0);

      handleSupplyFormChange({
        target: {
          name: 'amount_expected',
          value: totalAmount
        }
      });
    }
  }, [selectedInventoryItems, supplyFormData.is_custom_order]);

  // Calculate expected amount for custom orders
  useEffect(() => {
    if (supplyFormData.is_custom_order && customOrderItems.length > 0) {
      const totalAmount = customOrderItems.reduce((sum, item) => sum + item.total, 0);
      setSupplyFormData(prev => ({
        ...prev,
        amount_expected: totalAmount
      }));
    }
  }, [customOrderItems, supplyFormData.is_custom_order]);

  // Generate KRA eTim invoice for supply
  const handleGenerateInvoice = (supply) => {
    setSelectedSupply(supply);
    setShowInvoice(true);
  };

  // Generate KRA eTim invoice for sale
  const handleGenerateSaleInvoice = (sale) => {
    setSelectedSale(sale);
    setShowSaleInvoice(true);
  };


  // Handle selling a supply item
  const handleSellSupply = (supply) => {


    // if (supply.transaction?.receipts?.length > 0) {
    //   const receipt = supply.transaction.receipts.find(r => r.receipt_item == supply.item_name);
    //   if (receipt) {
    //     setSelectedSupply({
    //       ...supply,
    //       quantity: receipt.receipt_quantity,
    //       quantity_sold: 0,
    //       selling_price: parseFloat(supply.selling_price as unknown as string) || 0
    //     });
    //   }
    // } else {
      // Regular supply item
    setSelectedSupply(supply);


    // }

    setSaleFormData({
      ...saleFormData,
      supply_id: supply.supply_id,
      selling_price: parseFloat(supply.selling_price as unknown as string) || Math.round(parseFloat(supply.amount_expected as unknown as string) * 1.2)
    });

    setShowSaleForm(true);
  };

  // Print invoice
  const handlePrintInvoice = () => {
    window.print();
  };


  // Find supplier for a supply item
  const findSupplierForSupply = (supplyItem) => {
    if (!supplyItem || !supplyItem.supplier_id) return null;
    return suppliers.find(s => s.supplier_id == supplyItem.supplier_id);
  };

  // Table columns for suppliers
  const supplierColumns = [
    { label: 'Supplier ID', key: 'supplier_id', align: 'left' },
    { label: 'Name', key: 'supplier_name', align: 'left' },
    { label: 'Branch', key: 'supplier_branch', align: 'left' },
    { label: 'Contact', key: 'supplier_contact', align: 'left' },
    { label: 'Email', key: 'supplier_email', align: 'left' },
    { label: 'Status', key: 'supplier_status', align: 'left' },
    { label: 'Action', key: 'actions', align: 'center' }
  ];

  // Table columns for supplies
  const supplyColumns = [
    { label: 'Supply ID', key: 'supply_id', align: 'left' },
    { label: 'Supplier',key: 'supplier.supplier_name',align: 'left'},
    { label: 'Recipient', key: 'destination', align: 'left', render: (destination) => destination ? `${destination.supplier_name}${destination.supplier_branch ? ` (${destination.supplier_branch})` : ''}` : 'N/A' },
    // { label: 'Item', key: 'item_name', align: 'left' },
    { label: 'Original Quantity', key: 'quantity', align: 'left' },
    { label: 'Sold Quantity', key: 'quantity_sold', align: 'left' },
    { label: 'Available Quantity', key: 'available', align: 'left'},
    { label: 'Expected Amount', key: 'amount_expected', align: 'left' },
    { label: 'Paid Amount', key: 'amount_paid', align: 'left' },
    { label: 'Amount Due', key: 'amount_due', align: 'left' },
    // { label: 'Status', key: 'item_status.status_name', align: 'left' },
    // { label: 'Payment Status', key: 'payment_status', align: 'left' },
    { label: 'Date', key: 'supply_date', align: 'left'},
    { label: 'Action', key: 'actions', align: 'center' }
  ];

  // Table columns for sales
  const salesColumns = [
    { label: 'Sale ID', key: 'sale_id', align: 'left' },
    { label: 'Supply Item', key: 'item_name', align: 'left' },
    { label: 'Quantity Sold', key: 'quantity_sold', align: 'left' },
    { label: 'Unit Price', key: 'unit_price', align: 'left', render: (value) => value == 0 ? '-' : value},
    { label: 'Total Amount', key: 'total_amount', align: 'left' },
    { label: 'Customer', key: 'customer_name', align: 'left' },
    { label: 'Date', key: 'sale_date', align: 'left' },
    // { label: 'Payment Status', key: 'payment_status', align: 'left' },
    { label: 'Action', key: 'actions', align: 'center' }
  ];



  // const handleItemSelect = (receipt: any, isSelected: boolean) => {
  //   setSelectedItems(prev => {
  //     const newItems = { ...prev };
  //     if (isSelected) {
  //       newItems[receipt.receipt_item_id] = {
  //         trans_id: receipt.trans_id,
  //         receipt_id: receipt.receipt_id,
  //         receipt_item_id: receipt.receipt_item_id,
  //         item_name: receipt.receipt_item,
  //         quantity: receipt.receipt_quantity,
  //         unit_price: parseFloat(receipt.receipt_each) || 0,
  //         total: parseFloat(receipt.receipt_each) || 0,
  //         max_quantity: receipt.receipt_quantity
  //       };
  //     } else {
  //       delete newItems[receipt.receipt_item_id];
  //     }
  //     updateTotals(newItems);
  //     return newItems;
  //   });
  // };
  const handleItemSelect = (receipt: any, isSelected: boolean) => {
    setSelectedItems(prev => {
      const newItems = { ...prev };
  
      // Normalize keys across receipts and supply_receipts
      const receiptId = receipt.receipt_id || receipt.supply_receipt_id;
      const receiptItemId = receipt.receipt_item_id || receipt.item_id || receiptId; // fallback if item ID missing
      const quantity = parseFloat(receipt.receipt_quantity || 0);
      const unitPrice = parseFloat(receipt.receipt_each || 0);
  
      const existingItem = Object.values(newItems).find(
        item => item.receipt_item_id == receiptItemId
      );
  
      if (isSelected) {
        if (existingItem) {
          existingItem.quantity = Math.min(
            parseFloat(existingItem.quantity) + quantity,
            parseFloat(existingItem.max_quantity)
          );
          existingItem.total = existingItem.quantity * existingItem.unit_price;
        } else {
          newItems[receiptId] = {
            trans_id: receipt.trans_id,
            receipt_id: receiptId,
            receipt_item_id: receiptItemId,
            item_name: receipt.receipt_item,
            quantity: quantity,
            unit_price: unitPrice,
            total: quantity * unitPrice,
            max_quantity: quantity
          };
        }
      } else if (existingItem) {
        delete newItems[existingItem.receipt_id];
      }
  
      updateTotals(newItems);
      return newItems;
    });
  };
  

  const handleQuantityChange = (receiptId: string, newQuantity: number) => {
    setSelectedItems(prev => {
      const item = prev[receiptId];
      if (!item) return prev;
      const quantity = Math.min(Math.max(1, newQuantity), item.max_quantity);
      const total = quantity * item.unit_price;
      const newItems = {
        ...prev,
        [receiptId]: {
          ...item,
          quantity,
          total
        }
      };

      updateTotals(newItems);
      return newItems;
    });
  };

  const updateTotals = (items: Record<string, {}>) => {

    const total = Object.values(items).reduce((sum, item) => sum + parseInt(item.unit_price*item.quantity), 0);
    const totalQuantity = Object.values(items).reduce((sum, item) => sum + parseInt(item.quantity), 0);

    setTotalAmount(total);
    setQuantityToSell(totalQuantity);
  };

  const handleDeleteSupplier = async (supplierId: string) => {
    try {
      setIsLoading(true);
      await deleteData('suppliers', supplierId);
      loadSuppliersData();
      toaster.success({
        title: 'Supplier deleted',
        description: 'The supplier has been removed from your database.',
      });
    } catch (error) {
      toaster.error({
        title: 'Failed to delete supplier',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setIsLoading(false);
    }
  };


  const getSupplierActions = (item) => [
    {
      label: 'View Details',
      colorScheme: 'blue',
      onClick: () => handleViewSupplier(item)
    },
    {
      label: 'Edit',
      colorScheme: 'cyan',
      onClick: () => handleEditSupplier(item)
    },
    {
      label: 'Delete',
      colorScheme: 'red',
      onClick: () => handleDeleteSupplier(item.supplier_id)
    },
  ];
  const handleViewSupplier = (supplier) => {
    setSelectedSupplier(supplier);
    setShowSupplierDetails(true);
  };

const handleEditSupplier = (supplier) => {
    setSupplierFormData({
      supplier_name: supplier.supplier_name,
      supplier_branch: supplier.supplier_branch || '',
      supplier_contact: supplier.supplier_contact,
      supplier_email: supplier.supplier_email,
      supplier_address: supplier.supplier_address || '',
    });
    setSelectedSupplier(supplier);
    setIsEditing(true);
    setShowSupplierForm(true);
  };

  const handleFileReject = ( details ) => {

    if (!details.files || details.files.length == 0) return;
    const rejectedFiles = details.files.map((fileObj) => {
      const fileName = fileObj.file.name;
      const errorMessages = fileObj.errors?.map(err => err).join(", ") || "Unknown error";
      return `File: ${fileName}, Reason: ${errorMessages}`;
    });
    toaster.create({
      title: "File Rejected - Upload only CSV/XLSX",
      description: rejectedFiles.join("; "),
      type: "error",
    });
  };
  const selectBg = useColorModeValue('white', 'gray.700');
  const selectColor = useColorModeValue('gray.800', 'white');
  const selectBorderColor = useColorModeValue('gray.200', 'gray.600');
  const selectHoverBorderColor = useColorModeValue('gray.300', 'gray.500');
  const selectFocusBorderColor = 'blue.500';
  const selectFocusBoxShadow = '0 0 0 1px blue.500';


  // Get actions for supply table
  const getSupplyActions = (item) => {

    const availableQuantity = item.quantity - (item.quantity_sold || 0);

    const actions = [
      // { label: 'Generate Invoice', colorScheme: 'blue', onClick: () => handleGenerateInvoice(item) },
      {
        label: 'Attachments',
        colorScheme: 'cyan',
        onClick: () => handleViewAttachments(item)
      },
    ];
    if (availableQuantity> 0) {
      actions.push({ label: 'Sell Supply', colorScheme: 'purple', onClick: () => handleSellSupply(item) });
    }
    // if (item.amount_paid < item.amount_expected) {
    //   actions.push({ label: 'Request Payment', colorScheme: 'orange', onClick: () => handleOpenSupplyPaymentModal(item) });
    // }
    return actions;
  };

  // Get actions for sales table
  const getSaleActions = (item: SupplySale) => [
    { label: 'Generate Invoice', colorScheme: 'blue', onClick: () => handleGenerateSaleInvoice(item) },
    // { label: 'Request Payment', colorScheme: 'orange', onClick: () => handleOpenSalePaymentModal(item) },
    // { label: 'Edit Amounts', colorScheme: 'teal', onClick: () => handleOpenEditSaleModal(item) },
  ];

  const handleFileUpload = async (files) => {

    let file = files.files
    if (!file.length) return;
    const uniqueFiles = new Set();
    const filteredFiles = file.filter(file => {
      const isUnique = !uniqueFiles.has(file.name);
      uniqueFiles.add(file.name);
      return isUnique;
    });
    const existingFiles = new Set(acceptedFiles.map(file => file.name));
    const newFiles = filteredFiles.filter(file => !existingFiles.has(file.name));
    if (newFiles.length == 0) {
      toaster.create({
        title: "Duplicate File",
        description: "Files with the same name already exist. Please upload different files.",
        type: "warning",
      });
      return;
    }
    setAcceptedFiles(prevFiles => [...prevFiles, ...newFiles]);


    // setSupplyFormData(prev => ({
    //   ...prev,
    //   attachments: [...(prev.attachments || []), ...newFiles],
    // }));

    setSaleFormData(prev => ({
      ...prev,
      attachments: [...(prev.attachments || []), ...newFiles],
    }));

};

  const handleFileRemove = (fileToRemove) => {
    try {
      setAcceptedFiles(prevFiles => prevFiles.filter(file => file != fileToRemove));
      // setSupplyFormData(prev =>({...prev, attachments: prev.attachments.filter(file => file != fileToRemove.name),}));
      setSaleFormData(prev =>({...prev, attachments: prev.attachments.filter(file => file != fileToRemove.name),}));
    } catch (error) {
      toaster.create({
        title: "File Rejected",
        description: 'Failed to remove file: ' + error.message,
        type: "error",
      });
    }
  };

  useEffect(() => {
    if (selectedSupply?.destination_id) {
      setSaleFormData(prev => ({
        ...prev,
        buyer_supplier_id: selectedSupply.destination_id
      }));
    }
  }, [selectedSupply]);

  // Open payment modal for sale
  const handleOpenSalePaymentModal = (sale: SupplySale) => {
    setSelectedSaleForPayment(sale);
    const paid = (sale as any).amount_paid || 0;
    setSalePaymentAmount(sale.total_amount - paid);
    setShowSalePaymentModal(true);
    // load sale payment history
    fetchData<PaymentRecord>('supply_sale_payments', 1, 100, { sale_id: sale.sale_id })
      .then(res => res.data && setSalePaymentHistory(res.data || []))
      .catch(() => setSalePaymentHistory([]));
  };

  // Submit sale payment
  const handleSubmitSalePayment = async () => {
    if (!selectedSaleForPayment) return;
    try {
      const paidPrev = (selectedSaleForPayment as any).amount_paid || 0;
      const newPaid = paidPrev + salePaymentAmount;
      const status = newPaid >= selectedSaleForPayment.total_amount ? 'paid' : 'partial';
      await updateData('supply_sales', selectedSaleForPayment.sale_id, { amount_paid: newPaid, payment_status: status });
      // Upload evidence files
      for (const file of salePaymentFiles) {
        const form = new FormData();
        form.append('file', file);
        await fetchImage(`supply_sales/${selectedSaleForPayment.sale_id}/attachments`, form);
      }
      setSalePaymentFiles([]);
      toaster.success({ title: 'Payment recorded' });
      loadSupplySalesData();
    } catch (e) {
      toaster.error({ title: 'Payment failed', description: e instanceof Error ? e.message : String(e) });
    } finally {
      setShowSalePaymentModal(false);
    }
  };

  // Open sale edit modal
  const handleOpenEditSaleModal = (sale: SupplySale) => {
    setEditSaleData(sale);
    setShowEditSaleModal(true);
  };

  // Submit sale edit
  const handleSubmitEditSale = async () => {
    if (!editSaleData) return;
    try {
      const updatedTotal = editSaleData.quantity_sold! * editSaleData.selling_price!;
      await updateData('supply_sales', editSaleData.sale_id, {
        quantity_sold: editSaleData.quantity_sold,
        selling_price: editSaleData.selling_price,
        total_amount: updatedTotal
      });
      toaster.success({ title: 'Sale updated' });
      loadSupplySalesData();
    } catch (e) {
      toaster.error({ title: 'Update failed', description: e instanceof Error ? e.message : String(e) });
    } finally {
      setShowEditSaleModal(false);
    }
  };

  // Open supply payment modal
  const handleOpenSupplyPaymentModal = (item: SupplyItem) => {
    setSelectedSupplyForPayment(item);
    setSupplyPaymentAmount(item.amount_due || 0);
    setShowSupplyPaymentModal(true);
    // load supply payment history
    fetchData<PaymentRecord>('supply_payments', 1, 100, { supply_id: item.supply_id })
      .then(res => res.data && setSupplyPaymentHistory(res.data || []))
      .catch(() => setSupplyPaymentHistory([]));
  };

  const validateSale = () => {
    for (const item of Object.values(selectedItems)) {
      if (item.quantity > item.max_quantity) {
        toaster.error({ title: 'Update failed', description:`Quantity for ${item.item_name} exceeds available stock` });
        return false;
      }
      if (item.quantity <= 0) {
        toaster.error({ title: 'Update failed', description:`Invalid quantity for ${item.item_name}` });

        return false;
      }
    }
    return true;
  };

  // Submit supply payment with attachments
  const handleSubmitSupplyPayment = async () => {
    if (!selectedSupplyForPayment) return;
    try {
      const prevPaid = selectedSupplyForPayment.amount_paid;
      const newPaid = prevPaid + supplyPaymentAmount;
      const status = newPaid >= selectedSupplyForPayment.amount_expected ? 'paid' : 'partial';
      await updateData('supply_items', selectedSupplyForPayment.supply_id, {
        amount_paid: newPaid,
        status: status
      });
      // TODO: upload files via fetchImage or custom endpoint
      toaster.success({ title: 'Supply payment recorded' });
      loadSupplyItemsData();
    } catch (e) {
      toaster.error({ title: 'Payment error', description: e instanceof Error ? e.message : String(e) });
    } finally {
      setShowSupplyPaymentModal(false);
    }
  };




  // Enhanced stats cards data with comprehensive reports
  const statsCards = [
    {
      title: "Total Suppliers",
      icon: <FiPackage />,
      value: totalSuppliers,
      color: "cyan.300",
      isCurrency: false
    },
    // {
    //   title: "pending Supplies",
    //   icon: <FiCalendar />,
    //   value: (totalSupplies-totalSales)|| 0,
    //   color: "purple.500"
    // },
    {
      title: "Completed Supplies",
      icon: <FiShoppingCart />,
      value: totalSales || 0,
      color: "orange.500"
    },
    {
      title: "Pending payments",
      icon: <FiCheck />,
      value: pendingPayments || 0,
      color: "red.500",
      isCurrency: true
    },
    {
      title: "recent supply",
      icon: <FiPlus />,
      value: recentSupplies[0]?.item_name || 0,
      color: "yellow.500",
      isCurrency: false,
      isText: true
    }
  ];

  return (
    <Box p={4} ml={"1.5rem"}>

      {isLoading && (
            <Flex
              position="fixed"
              top="0"
              left="0"
              height="100vh"
              width="100vw"
              backgroundColor="rgba(0, 0, 0, 0.5)"
              justifyContent="center"
              alignItems="center"
              zIndex="9999"
            >
              <Spinner size="xl" color="red.600" borderWidth="4px"/>
            </Flex>
          )}
      <Heading size="lg" mb={6}>Supplier Management</Heading>

      {/* Time frame selector and actions */}
      <HStack gap={4} mb={6} justifyContent="space-between" direction="column">
        <HStack align="center" gap={4}>
              {/* <VStack>
                <Text>Select time frame</Text> */}
                    <NativeSelectRoot
                      size="md"
                      value={timeFrame}
                      onChange={(e) => handleTimeFrameChange(e)}
                      width="200px"
                    >
                      <NativeSelectField>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                        <option value="yearly">Yearly</option>
                        <option value="custom">Custom Range</option>
                      </NativeSelectField>
                    </NativeSelectRoot>
                 {/* </VStack> */}
                 {timeFrame == 'custom' && (
                   <>
                     <HStack align="center" gap={1}>
                       <Text textStyle="xs">Start:</Text>
                       <Input
                         type="date"
                         value={startDate}
                         onChange={e => setStartDate(e.target.value)}
                         size="xs"
                         max={endDate || undefined}
                       />
                     </HStack>

                     <HStack align="center" gap={1}>
                       <Text textStyle="xs">End:</Text>
                       <Input
                         type="date"
                         value={endDate}
                         onChange={e => setEndDate(e.target.value)}
                         size="xs"
                         min={startDate || undefined}
                       />
                     </HStack>

                     <Button
                       colorScheme="blue"
                       size="xs"
                       disabled={!startDate || !endDate}
                       _loading={isLoading}
                       onClick={loadTabData}
                     >
                       Apply
                     </Button>
                     </>
                 )}
        </HStack>

        <HStack
          align={{ base: 'start', md: 'end' }}
          justify={{ base: 'flex-start', md: 'flex-end' }}
          flexWrap="wrap"
          gap={2}
        >
          <Button  colorPalette="cyan" onClick={() => setShowSupplierForm(true)}>
          <FiPlus /> Add Supplier
          </Button>
          <Button  colorPalette="blue" onClick={() => setShowSupplyForm(true)}>
          <FiPlus />Add Supply Order
          </Button>
       
        </HStack>
      </HStack>

      {/* Stats Cards */}
      <Grid templateColumns={{ base: "repeat(1, 1fr)", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)", xl: "repeat(4, 1fr)" }} gap={6} mb={6}>
        {statsCards.map((stat, index) => (
          <Card.Root key={index} p={4} borderRadius="lg" boxShadow="sm">
            <Stack direction="column" gap={2}>
              <HStack justifyContent="space-between">
                <Heading color={stat.color} size="sm" fontWeight="semibold">
                  {stat.title}
                </Heading>
                <Box color={stat.color} fontSize="xl">
                  {stat.icon}
                </Box>
              </HStack>
              <StatRoot>
                {stat.isText ? (
                  <Text fontSize="xl" fontWeight="bold">{stat.value}</Text>
                ) : (
                  <StatValueText
                    textStyle="xl"
                    fontWeight="bold"
                    value={stat.value}
                    formatOptions={stat.isCurrency ? { style: "currency", currency: "KSH" } : undefined}
                  />
                )}
                <StatHelpText fontSize="sm" color="gray.500">
                  {timeFrame ? `${timeFrame.charAt(0).toUpperCase() + timeFrame.slice(1)} report` : 'Report'}
                </StatHelpText>
              </StatRoot>
            </Stack>
          </Card.Root>
        ))}
      </Grid>

      {/* Best Selling Items Section */}
      {bestSellingItems.length > 0 && (
        <Card.Root mb={6} p={4} borderRadius="lg" boxShadow="sm">
          <Card.Header>
            <Heading size="md" color="blue.600">Best Selling Items</Heading>
          </Card.Header>
          <Card.Body>
            <Grid templateColumns={{ base: "repeat(1, 1fr)", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={4}>
              {bestSellingItems.slice(0, 4).map((item, index) => (
                <Box key={item.item_id} p={3} borderWidth={1} borderRadius="md" >
                  <VStack align="start" gap={2}>
                    <HStack justify="space-between" w="full">
                      <Text fontWeight="bold" fontSize="sm" isTruncated>{item.item_name}</Text>
                      <Badge colorScheme={item.profit_margin >= 0 ? "cyan" : "teal"} size="sm">
                        {item.profit_margin}%
                      </Badge>
                    </HStack>
                    <Text fontSize="xs" color="gray.600">{item.category_name}</Text>
                    <SimpleGrid columns={2} gap={2} w="full">
                      <Box>
                        <Text fontSize="xs" color="gray.500">Units Sold</Text>
                        <Text fontWeight="bold" fontSize="sm">{item.units_sold}</Text>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">Revenue</Text>
                        <Text fontWeight="bold" fontSize="sm" color="cyan.600">
                          {formatCurrency(item.actual_revenue)}
                        </Text>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">Profit</Text>
                        <Text fontWeight="bold" fontSize="sm" color={item.total_profit >= 0 ? "cyan.600" : "teal.600"}>
                          {formatCurrency(item.total_profit)}
                        </Text>
                      </Box>
                      <Box>
                        <Text fontSize="xs" color="gray.500">Price</Text>
                        <Text fontWeight="bold" fontSize="sm">{formatCurrency(item.item_selling)}</Text>
                      </Box>
                    </SimpleGrid>
                  </VStack>
                </Box>
              ))}
            </Grid>
          </Card.Body>
        </Card.Root>
      )}

      {/* Tabs for different supplier data */}
      <Tabs.Root variant="enclosed" colorScheme="blue" mb={6} value={activeTab} onValueChange={handleTabChange}>
        <Tabs.List>
          <Tabs.Trigger value='suppliers'>Suppliers</Tabs.Trigger>
          <Tabs.Trigger value='supplies'>Supplies</Tabs.Trigger>
          <Tabs.Trigger value='sales'>Supply Sales</Tabs.Trigger>
          <Tabs.Trigger value='buyers'>Buyers</Tabs.Trigger>
        </Tabs.List>

        <Suspense fallback={<Center h={'70vh'}><Spinner size="xl" /></Center>}>
        <Tabs.Content value='buyers'>
              <HStack justify="space-between" mb={4}>
                <Search
                  placeholder="Search ..."
                  tableName="buyers"
                  onSearch={handleSearch}
                />
                <HStack>
                  <Button size="sm" colorPalette="cyan" onClick={() => exportReport('buyers', 'csv')}>
                    Export CSV
                  </Button>
                  <Button size="sm" colorPalette="teal" onClick={() => exportReport('buyers', 'pdf')}>
                    Export PDF
                  </Button>
                </HStack>
              </HStack>
              {loadingStates.sales ? (
                <Center h={'50vh'}><Spinner size="xl" /></Center>
              ) : (
              <VStack gap={4} align="stretch">
                {Object.entries(supplySales.reduce((acc, sale) => {
                  const buyerKey = sale.customer_name || 'Walk-in Customer';
                  const groupKey = sale.supply_branch ? `${buyerKey} (${sale.supply_branch})` : buyerKey;
                  if (!acc[groupKey]) {
                    acc[groupKey] = [];
                  }
                  acc[groupKey].push(sale);
                  return acc;
                }, {})).map(([buyerKey, sales]) => (

                <Accordion.Root key={buyerKey} variant="outline" size="md" collapsible>
                  <Accordion.Item>
                    <Accordion.ItemTrigger>
                    <HStack
                      justify="space-between"
                      width="100%"
                      pr={4}
                      gap={6}
                    >
                    <Box flex={1}>
                      <Text fontWeight="bold">{buyerKey}</Text>
                    </Box>
                    <Box flex={1} textAlign="center">
                      <Text>Sales: {sales?.length}</Text>
                    </Box>
                    <Box flex={1} textAlign="right">
                      <Text>
                        Revenue: {
                          sales?.length ?
                            new Intl.NumberFormat('en-KE', {
                              style: 'currency',
                              currency: 'KES',
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2
                            }).format(
                              sales?.reduce((sum, sale) => sum + (Number(sale?.total_amount) || 0), 0)
                            )
                          : 'KES 0.00'
                        }
                      </Text>
                    </Box>
                    <Box>
                      <Accordion.ItemIndicator />
                    </Box>
                  </HStack>
                    </Accordion.ItemTrigger>
                    <Accordion.ItemContent>
                      <Accordion.ItemBody>
                        <Table.Root variant="outline" size="sm">
                          <Table.Header>
                            <Table.Row>
                              <Table.ColumnHeader>Date</Table.ColumnHeader>
                              <Table.ColumnHeader>Item</Table.ColumnHeader>
                              <Table.ColumnHeader>Quantity</Table.ColumnHeader>
                              <Table.ColumnHeader>Total Amount</Table.ColumnHeader>
                              {/* <Table.ColumnHeader>Status</Table.ColumnHeader> */}
                            </Table.Row>
                          </Table.Header>
                          <Table.Body>
                            {sales?.map((sale) =>(
                              <Table.Row key={sale.sale_id}>
                                <Table.Cell>{new Date(sale.sale_date).toLocaleDateString()}</Table.Cell>
                                <Table.Cell>{sale.item_name}</Table.Cell>
                                <Table.Cell>{sale.quantity_sold}</Table.Cell>
                                <Table.Cell>{Number(sale?.total_amount).toFixed(2)}</Table.Cell>
                                {/* <Table.Cell>{sale.payment_status}</Table.Cell> */}
                              </Table.Row>
                            ))}
                          </Table.Body>
                        </Table.Root>
                      </Accordion.ItemBody>
                    </Accordion.ItemContent>
                  </Accordion.Item>
                </Accordion.Root>
                ))}
              </VStack>
              )}
            </Tabs.Content>



          <Tabs.Content value='suppliers'>
            <HStack justify="space-between" mb={4}>
              <Search
                placeholder="input"
                tableName="suppliers"
                onSearch={handleSearch}
              />
              <HStack>
                <Button size="sm" colorPalette="cyan" onClick={() => exportReport('suppliers', 'csv')}>
                  Export CSV
                </Button>
                <Button size="sm" colorPalette="teal" onClick={() => exportReport('suppliers', 'pdf')}>
                  Export PDF
                </Button>
              </HStack>
            </HStack>
            {loadingStates.suppliers ? (
              <Center h={'50vh'}><Spinner size="xl" /></Center>
            ) : (
              <Box w={'100%'}>
                <Table_default
                  columns={supplierColumns}
                  data={suppliers}
                  getActions={getSupplierActions}
                  currentPage={pagination.suppliers.currentPage}
                  pageSize={pagination.suppliers.pageSize}
                  totalItems={pagination.suppliers.totalRecords}
                  handlePageChange={handlePageChange}
                  handlePageSizeChange={handlePageSizeChange}
                />
              </Box>
            )}
          </Tabs.Content>

          <Tabs.Content value='supplies'>
              <HStack justify="space-between" mb={4}>
                <Search
                  placeholder="Search ..."
                  tableName="supplies"
                  onSearch={handleSearch}
                />
                <HStack>
                  <Button size="sm" colorPalette="cyan" onClick={() => exportReport('supplies', 'csv')}>
                    Export CSV
                  </Button>
                  <Button size="sm" colorPalette="teal" onClick={() => exportReport('supplies', 'pdf')}>
                    Export PDF
                  </Button>
                </HStack>
              </HStack>
              {loadingStates.supplies ? (
                <Center h={'50vh'}><Spinner size="xl" /></Center>
              ) : (
                <Box w={'100%'}>
                  <Table_default
                    columns={supplyColumns}
                    data={supplyItems}
                    getActions={getSupplyActions}
                    currentPage={pagination.supplies.currentPage}
                    pageSize={pagination.supplies.pageSize}
                    totalItems={pagination.supplies.totalRecords}
                    handlePageChange={handlePageChange}
                    handlePageSizeChange={handlePageSizeChange}
                  />
                </Box>
              )}
            </Tabs.Content>

          <Tabs.Content value='sales'>
            <HStack justify="space-between" mb={4}>
              <Search
                placeholder="Search ..."
                tableName="sales"
                onSearch={handleSearch}
              />
              <HStack>
                <Button size="sm" colorPalette="cyan" onClick={() => exportReport('sales', 'csv')}>
                  Export CSV
                </Button>
                <Button size="sm" colorPalette="teal" onClick={() => exportReport('sales', 'pdf')}>
                  Export PDF
                </Button>
              </HStack>
            </HStack>
            {loadingStates.sales ? (
              <Center h={'50vh'}><Spinner size="xl" /></Center>
            ) : (
              <Box w={'100%'}>
                <Table_default
                  columns={salesColumns}
                  data={supplySales}
                  getActions={getSaleActions}
                  currentPage={pagination.sales.currentPage}
                  pageSize={pagination.sales.pageSize}
                  totalItems={pagination.sales.totalRecords}
                  handlePageChange={handlePageChange}
                  handlePageSizeChange={handlePageSizeChange}
                />
              </Box>
            )}
          </Tabs.Content>
        </Suspense>
      </Tabs.Root>

      {/* Add Supplier Modal */}
      <DialogRoot open={showSupplierForm} onOpenChange={(e) => setShowSupplierForm(e.open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Supplier</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <VStack gap={4} align="stretch">
              <Field.Root>
                <Field.Label>Supplier Name</Field.Label>
                <Input
                  name="supplier_name"
                  value={supplierFormData.supplier_name}
                  onChange={handleSupplierFormChange}
                  placeholder="Enter supplier name"
                />
              </Field.Root>
              <Field.Root>
                <Field.Label>Supplier Branch</Field.Label>
                <Input
                  name="supplier_branch"
                  value={supplierFormData.supplier_branch}
                  onChange={handleSupplierFormChange}
                  placeholder="Enter supplier branch"
                />
              </Field.Root>

              <Field.Root>
                <Field.Label>Contact Number</Field.Label>
                <Input
                  name="supplier_contact"
                  value={supplierFormData.supplier_contact}
                  onChange={handleSupplierFormChange}
                  placeholder="Enter contact number"
                />
              </Field.Root>

              <Field.Root>
                <Field.Label>Email Address</Field.Label>
                <Input
                  name="supplier_email"
                  value={supplierFormData.supplier_email}
                  onChange={handleSupplierFormChange}
                  placeholder="Enter email address"
                  type="email"
                />
              </Field.Root>

              <Field.Root>
                <Field.Label>Address (Optional)</Field.Label>
                <Input
                  name="supplier_address"
                  value={supplierFormData.supplier_address}
                  onChange={handleSupplierFormChange}
                  placeholder="Enter address"
                />
              </Field.Root>
            </VStack>
          </DialogBody>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSupplierForm(false)}>Cancel</Button>
            <Button colorScheme="blue" onClick={handleSupplierSubmit} isLoading={isLoading}>Save Supplier</Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>

      {/* Add Supply Modal */}
      <DialogRoot open={showSupplyForm} onOpenChange={(e) => setShowSupplyForm(e.open)} size={'lg'}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Supply/Order</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <VStack gap={4} align="stretch">
              <HStack>
                <Field.Root flex="1">
                  <Field.Label>Supply From</Field.Label>
                  <InfiniteScrollDropdown
                    name="supplier_id"
                    value={supplyFormData.supplier_id}
                    onChange={(value) => handleSupplyFormChange({
                      target: {
                        name: 'supplier_id',
                        value: value
                      }
                    })}
                    placeholder="Select a Supplier"
                    shop_id={shop_id}
                    endpoint="suppliers"
                    displayField="supplier_name_with_branch"
                    valueField="supplier_id"
                    bg={selectBg}
                    color={selectColor}
                    borderColor={selectBorderColor}
                    _hover={{ borderColor: selectHoverBorderColor }}
                    _focus={{
                      borderColor: selectFocusBorderColor,
                      boxShadow: selectFocusBoxShadow
                    }}
                  />
                </Field.Root>

                <Field.Root flex="1">
                  <Field.Label>Supply To</Field.Label>
                  <InfiniteScrollDropdown
                    name="destination_id"
                    value={supplyFormData.destination_id}
                    onChange={(value) => handleSupplyFormChange({
                      target: {
                        name: 'destination_id',
                        value: value
                      }
                    })}
                    placeholder="Select a Destination"
                    shop_id={shop_id}
                    endpoint="suppliers"
                    displayField="supplier_name_with_branch"
                    bg={selectBg}
                    valueField="supplier_id"
                    color={selectColor}
                    borderColor={selectBorderColor}
                    _hover={{ borderColor: selectHoverBorderColor }}
                    _focus={{
                      borderColor: selectFocusBorderColor,
                      boxShadow: selectFocusBoxShadow,
                    }}
                  />
                </Field.Root>
              </HStack>


                  <Box display="flex" justifyContent="center" width="100%">
                  <HStack gap={4} justify="center" width="100%">
                    <Text
                      fontSize="sm"
                      fontWeight={!supplyFormData.is_custom_order ? "bold" : "normal"}
                      color={!supplyFormData.is_custom_order ? "blue.500" : "gray.500"}
                    >
                      Inventory Order
                    </Text>

                    <Switch.Root
                      size="lg"
                      checked={supplyFormData.is_custom_order}
                      onCheckedChange={(details) => {
                        const newValue = details.checked;
                        handleSupplyFormChange({
                          target: {
                            name: 'is_custom_order',
                            type: 'checkbox',
                            checked: newValue
                          }
                        });
                        if (!newValue) {
                          fetchInventoryItems();
                        }
                      }}
                    >
                      <Switch.HiddenInput />
                      <Switch.Control>
                        <Switch.Thumb />
                      </Switch.Control>
                    </Switch.Root>

                    <Text
                      fontSize="sm"
                      fontWeight={supplyFormData.is_custom_order ? "bold" : "normal"}
                      color={supplyFormData.is_custom_order ? "blue.500" : "gray.500"}
                    >
                      Custom Order
                    </Text>
                  </HStack>
              </Box>
              {supplyFormData.is_custom_order ? (
                <Box>
                  <Text fontWeight="medium" mb={3}>Custom Order Items</Text>

                  {/* Add New Item Form */}
                  <Box borderWidth="1px" borderRadius="md" p={4} mb={4}>
                    <Text fontWeight="medium" mb={3}>Add New Item</Text>
                    <VStack gap={3}>
                      <Field.Root>
                        <Field.Label>Item Name</Field.Label>
                        <Input
                          name="item_name"
                          value={newCustomItem.item_name}
                          onChange={handleNewCustomItemChange}
                          placeholder="Enter item name"
                        />
                      </Field.Root>

                      <HStack gap={3} width="100%">
                        <Field.Root flex="1">
                          <Field.Label>Quantity</Field.Label>
                          <Input
                            name="quantity"
                            value={newCustomItem.quantity}
                            onChange={handleNewCustomItemChange}
                            placeholder="Enter quantity"
                            type="number"
                            min="1"
                          />
                        </Field.Root>

                        <Field.Root flex="1">
                          <Field.Label>Unit Price</Field.Label>
                          <Input
                            name="unit_price"
                            value={newCustomItem.unit_price}
                            onChange={handleNewCustomItemChange}
                            placeholder="Enter unit price"
                            type="number"
                            min="0"
                            step="0.01"
                          />
                        </Field.Root>

                        <Box pt={6}>
                          <Button
                            colorScheme="blue"
                            onClick={addCustomOrderItem}
                     
                          >
                           <FiPlus /> Add Item
                          </Button>
                        </Box>
                      </HStack>
                    </VStack>
                  </Box>

                  {/* Custom Order Items List */}
                  {customOrderItems.length > 0 && (
                    <Box borderWidth="1px" borderRadius="md" overflow="hidden">
                      <Table.Root size="sm">
                        <Table.Header>
                          <Table.Row>
                            <Table.ColumnHeader>Item Name</Table.ColumnHeader>
                            <Table.ColumnHeader>Quantity</Table.ColumnHeader>
                            <Table.ColumnHeader>Unit Price</Table.ColumnHeader>
                            <Table.ColumnHeader>Total</Table.ColumnHeader>
                            <Table.ColumnHeader>Actions</Table.ColumnHeader>
                          </Table.Row>
                        </Table.Header>
                        <Table.Body>
                          {customOrderItems.map((item) => (
                            <Table.Row key={item.id}>
                              <Table.Cell>
                                <Input
                                  value={item.item_name}
                                  onChange={(e) => updateCustomOrderItem(item.id, 'item_name', e.target.value)}
                                  size="sm"
                                />
                              </Table.Cell>
                              <Table.Cell>
                                <Input
                                  type="number"
                                  value={item.quantity}
                                  onChange={(e) => updateCustomOrderItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                                  size="sm"
                                  min="1"
                                />
                              </Table.Cell>
                              <Table.Cell>
                                <Input
                                  type="number"
                                  value={item.unit_price}
                                  onChange={(e) => updateCustomOrderItem(item.id, 'unit_price', parseFloat(e.target.value) || 0)}
                                  size="sm"
                                  min="0"
                                  step="0.01"
                                />
                              </Table.Cell>
                              <Table.Cell>
                                <Text fontWeight="medium">{formatCurrency(item.total)}</Text>
                              </Table.Cell>
                              <Table.Cell>
                                <Button
                                  size="sm"
                                  colorScheme="teal"
                                  variant="ghost"
                                  onClick={() => removeCustomOrderItem(item.id)}
                                >
                                  <FiX />
                                </Button>
                              </Table.Cell>
                            </Table.Row>
                          ))}
                        </Table.Body>
                      </Table.Root>

                      {/* Total Summary */}
                      <Box p={3}  borderTop="1px solid" borderColor="gray.200">
                        <HStack justify="space-between">
                          <Text fontWeight="medium">Total Items: {customOrderItems.length}</Text>
                          <Text fontWeight="medium">
                            Total Quantity: {customOrderItems.reduce((sum, item) => sum + item.quantity, 0)}
                          </Text>
                          <Text  fontSize="md">
                            Expected Amount:  </Text> <Text  fontWeight="bold" color='cyan'>{formatCurrency(customOrderItems.reduce((sum, item) => sum + item.total, 0))}</Text>
                        
                        </HStack>
                      </Box>
                    </Box>
                  )}

                  {customOrderItems.length == 0 && (
                    <Box textAlign="center" py={8} color="gray.500">
                      <Text>No items added yet. Add your first item above.</Text>
                    </Box>
                  )}
                </Box>
              ) : (
                <Box>
                  <HStack justify="space-between" mb={2}>
                    <Text fontWeight="medium">Select Items from Inventory</Text>
                    {/* <Checkbox.Root
                      colorPalette="blue"
                      size="md"
                      checked={selectedInventoryItems.length > 0 && selectedInventoryItems.length == inventoryItems.length}
                      onCheckedChange={() => handleSelectAllInventoryItems(!(selectedInventoryItems.length > 0 && selectedInventoryItems.length == inventoryItems.length))}
                    >
                      <Checkbox.HiddenInput />
                      <Checkbox.Control>
                        <Checkbox.Indicator />
                      </Checkbox.Control>
                      <Checkbox.Label>Select All</Checkbox.Label>
                    </Checkbox.Root> */}
                  </HStack>

                  <Input
                    placeholder="Search inventory items..."
                    value={inventorySearchQuery}
                    onChange={(e) => setInventorySearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key == 'Enter' && handleInventorySearch(inventorySearchQuery)}
                    mb={2}
                  />

                  <Box maxH="300px" overflowY="auto" borderWidth="1px" borderRadius="md">
                    <Table.Root size="sm">
                      <Table.Header position="sticky" top={0} zIndex={1}>
                        <Table.Row>
                          <Table.ColumnHeader width="50px"></Table.ColumnHeader>
                          <Table.ColumnHeader>Item Name</Table.ColumnHeader>
                          <Table.ColumnHeader>Availability</Table.ColumnHeader>
                          <Table.ColumnHeader>Unit price</Table.ColumnHeader>
                          <Table.ColumnHeader>Quantity</Table.ColumnHeader>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {isLoadingInventory ? (
                          <Table.Row>
                            <Table.Cell colSpan={4} textAlign="center" py={4}>
                              <Spinner size="md" color="teal.600" />
                            </Table.Cell>
                          </Table.Row>
                        ) : inventoryItems.length == 0 ? (
                          <Table.Row>
                            <Table.Cell colSpan={4} textAlign="center" py={4}>
                              No inventory items found
                            </Table.Cell>
                          </Table.Row>
                        ) : (
                          inventoryItems.map((item) => {
                            const isSelected = selectedInventoryItems.some(i => i.item_id == item.item_id);
                            const selectedItem = selectedInventoryItems.find(i => i.item_id == item.item_id);

                            return (
                              <Table.Row key={item.item_id}>
                                <Table.Cell>
                                  <Checkbox.Root
                                    colorPalette="blue"
                                    size="md"
                                    checked={isSelected}
                                    onCheckedChange={() => handleInventoryItemSelect(item, !isSelected)}
                                    disabled={item.item_quantity <= 0}
                                  >
                                    <Checkbox.HiddenInput />
                                    <Checkbox.Control>
                                      <Checkbox.Indicator />
                                    </Checkbox.Control>
                                  </Checkbox.Root>
                                </Table.Cell>
                                <Table.Cell truncate>{item.item_name}</Table.Cell>
                                <Table.Cell>{item.item_availability ? "in stock" : "out of stock"}</Table.Cell>
                                <Table.Cell>{item.item_selling }</Table.Cell>
                                <Table.Cell>{item.item_quantity}</Table.Cell>

                                <Table.Cell>
                                  {isSelected && (
                                    <Input
                                      type="number"
                                      size="sm"
                                      min={1}
                                      max={item.item_quantity}
                                      value={selectedItem?.quantity || 1}
                                      onChange={(e) => handleInventoryItemQuantityChange(item.item_id, parseInt(e.target.value) || 1)}
                                      disabled={!isSelected}
                                    />
                                  )}
                                </Table.Cell>
                              </Table.Row>
                            );
                          })
                        )}
                      </Table.Body>
                    </Table.Root>
                  </Box>

                  {selectedInventoryItems.length > 0 && (

                    <Box borderTop="1px solid" borderColor="gray.100" pt={3} mt={3}>
                    <Flex justify="space-between" fontSize="xs" color="gray.500" mb={3}>
                      <span>Selected ({selectedInventoryItems.length})</span>
                      <span>Total: {selectedInventoryItems.reduce((sum, item) => sum + item.quantity, 0)}</span>
                    </Flex>

                    <SimpleGrid
                      columns={2}
                      gapX={4}
                      gapY={3}
                      borderTop="1px solid"
                      borderColor="gray.100"
                      pt={2}
                    >
                      {selectedInventoryItems.map(item => (
                        <Flex
                          key={item.item_id}
                          align="baseline"
                          gap={2}
                        >
                          <Text fontSize="sm" truncate maxW="65%">{item.item_name}</Text>
                          <Text fontSize="sm" color="blue.600" fontWeight="500">×{item.quantity}</Text>
                        </Flex>
                      ))}
                    </SimpleGrid>
                  </Box>
                  )}
                </Box>
              )}
              <HStack>

                <Field.Root>
                  <Field.Label>Expected Total Amount</Field.Label>
                  <Input
                    name="amount_expected"
                    value={supplyFormData.amount_expected}
                    onChange={handleSupplyFormChange}
                    placeholder={
                      (selectedInventoryItems.length > 0 && !supplyFormData.is_custom_order) ||
                      (customOrderItems.length > 0 && supplyFormData.is_custom_order)
                        ? "Calculated automatically"
                        : "Enter expected amount"
                    }
                    type="number"
                    readOnly={
                      (selectedInventoryItems.length > 0 && !supplyFormData.is_custom_order) ||
                      (customOrderItems.length > 0 && supplyFormData.is_custom_order)
                    }
                  />
                </Field.Root>

                <Field.Root>
                  <Field.Label>Full Amount Already Paid </Field.Label>
                  <Input
                    name="amount_paid"
                    value={supplyFormData.amount_paid}
                    onChange={handleSupplyFormChange}
                    placeholder="Enter paid amount"
                    type="number"
                  />
                </Field.Root>
              </HStack>
              <Field.Root>
                  <Text fontWeight="bold" fontSize="sm" mb="2">Supply Date
                  </Text>
                  <HStack>
                    <Input
                      type="date"
                      value={supplyFormData.supply_date}
                      onChange={(e) => setSupplyFormData(prev => ({...prev, supply_date: e.target.value}))}
                      placeholder="Select supply date"
                    />
                  </HStack>
                  </Field.Root>
              <Field.Root>
                <Field.Label>Attachments</Field.Label>
                <FileUploadRoot maxW="xl" maxFiles={5} maxFileSize={5000000}
                  onFileAccept={handleFileUpload}
                  onFileReject={handleFileReject}
                  allowDrop={true}
                  accept={{
                    'image/*': ['.png', '.jpg', '.jpeg'],
                    'application/pdf': ['.pdf'],
                    'text/csv': ['.csv'],
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
                    'application/vnd.ms-excel': ['.xls'],
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
                  }}
                >
                  <HStack>
                    <FileUploadTrigger asChild>
                      <Button variant="outline" size="sm"><FiUpload />Upload file</Button>
                    </FileUploadTrigger>
                    <Box flex="1">
                      {acceptedFiles.length > 0 ? (
                        <List.Root>
                          {acceptedFiles.map((file, index) => (
                            <List.Item key={index} display="flex" alignItems="center" gap={2}>
                              <Box display="flex" alignItems="center">
                                <Text fontSize="md">{file.name}</Text>
                                <Icon
                                  ml={6}
                                  color="tomato"
                                  onClick={() => handleFileRemove(file)}
                                  cursor="pointer"
                                >
                                  <FiX />
                                </Icon>
                              </Box>
                            </List.Item>
                          ))}
                        </List.Root>
                      ) : (
                        <Text>No Files uploaded yet.</Text>
                      )}
                    </Box>
                  </HStack>
                </FileUploadRoot>
              </Field.Root>
            </VStack>
          </DialogBody>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowSupplyForm(false);
              setSelectedSupply(null);
              setAcceptedFiles([]);
              setSupplyFormData({
                supplier_id: '',
                destination_id: '',
                item_name: '',
                quantity: 0,
                amount_expected: 0,
                amount_paid: 0,
                supply_date: new Date().toISOString().split('T')[0],
                attachments: [],
                is_custom_order: true
              });
              setSelectedInventoryItems([]);
              setCustomOrderItems([]);
              setNewCustomItem({
                item_name: '',
                quantity: 1,
                unit_price: 0
              });
            }}>Cancel</Button>
            <Button colorScheme="blue" onClick={handleSupplySubmit} disabled={isLoading}>Save Supply</Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>

      {/* Add Sale Modal */}
      <DialogRoot open={showSaleForm} onOpenChange={(e) => setShowSaleForm(e.open)} size="lg">
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Sell Supply Item</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <VStack gap={4} align="stretch">
            {selectedSupply && selectedSupply.transaction && (
                      <Stack>
                        <Box p={3} borderWidth="1px" borderRadius="md">
                          <Text fontWeight="bold">Supply Destination: {selectedSupply.item_name}</Text>
                          <Text>
                            Available Quantity:{" "}
                            {(parseFloat(selectedSupply?.quantity) -
                              parseFloat(selectedSupply?.quantity_sold || "0")).toString()}
                          </Text>
                          <Text color="cyan">Expected Amount: {selectedSupply.amount_expected}</Text>
                          <Text>Transaction ID: {selectedSupply.transaction.trans_id}</Text>
                        </Box>

                        <Box borderWidth="1px" borderRadius="md" w="100%">
                          <Text fontWeight="bold" p={2}>
                            Items
                          </Text>

                          {(selectedSupply.transaction?.receipts?.length > 0 ||
                            selectedSupply.transaction?.supply_receipts?.length > 0) ? (
                            <Table.Root variant="outline" size="sm">
                              <Table.Header>
                                <Table.Row>
                                  <Table.ColumnHeader></Table.ColumnHeader>
                                  <Table.ColumnHeader>ID</Table.ColumnHeader>
                                  <Table.ColumnHeader>Item</Table.ColumnHeader>
                                  <Table.ColumnHeader>Unit Price</Table.ColumnHeader>
                                  <Table.ColumnHeader>(Qty) Ordered</Table.ColumnHeader>
                                  <Table.ColumnHeader>InStock</Table.ColumnHeader>
                                  <Table.ColumnHeader>Selling</Table.ColumnHeader>
                                </Table.Row>
                              </Table.Header>
                              <Table.Body>
                                {[...(selectedSupply.transaction.receipts || []), ...(selectedSupply.transaction.supply_receipts || [])].map(
                                  (receipt, index) => {
                                    // Normalize supply_receipt to have receipt_id for consistent handling
                                    if (receipt.supply_receipt_id && !receipt.receipt_id) {
                                      receipt.receipt_id = receipt.supply_receipt_id;
                                    }
                                  
                                    
                                    const id = receipt.receipt_id ? receipt.receipt_id : receipt.supply_receipt_id;
                                    // const id = receipt.receipt_id || `supply_${receipt.supply_receipt_id}`;


                                    const isSelected = !!selectedItems[id];
                                    return (
                                      <Table.Row key={`${id}-${index}`}>
                                        <Table.Cell onClick={(e) => e.stopPropagation()}>
                                          {parseFloat(receipt.receipt_quantity) > 0 &&
                                          (<Checkbox.Root
                                            disabled={parseFloat(receipt.receipt_quantity) <= 0}
                                            colorPalette="blue"
                                            variant="solid"
                                            value={id}
                                            checked={isSelected}
                                            onCheckedChange={(e) =>  handleItemSelect(receipt, !!e.checked) }
                                          >
                                            <Checkbox.HiddenInput />
                                            <Checkbox.Control style={{ cursor: "pointer" }}>
                                              <Checkbox.Indicator />
                                            </Checkbox.Control>
                                          </Checkbox.Root>)}

                                        </Table.Cell>
                                        <Table.Cell>{id?.toString().toLowerCase() || "N/A"}</Table.Cell>
                                        <Table.Cell truncate>{receipt.receipt_item}</Table.Cell>
                                        <Table.Cell>{receipt.receipt_each || "0.00"}</Table.Cell>
                                        <Table.Cell>{receipt.receipt_quantity || "0"}</Table.Cell>
                                        <Table.Cell>{receipt.item?.item_quantity || "0"}</Table.Cell>
                                        <Table.Cell>
                                          {isSelected ? (
                                            <Input
                                              type="number"
                                              step="any"
                                              value={selectedItems[id]?.quantity}
                                              onChange={(e) => {
                                                const value = parseFloat(e.target.value);
                                                if (!isNaN(value) && value > 0) {
                                                  handleQuantityChange(id, value);
                                                }
                                              }}
                                              onClick={(e) => e.stopPropagation()}
                                              size="xs"
                                              width="80px"
                                            />
                                          ) : (
                                            receipt.receipt_quantity
                                          )}
                                        </Table.Cell>
                                      </Table.Row>
                                    );
                                  }
                                )}
                              </Table.Body>
                            </Table.Root>
                          ) : (
                            <Text>No receipt items found</Text>
                          )}
                        </Box>

                        <Box mt={4} p={3} borderWidth="1px" borderRadius="md">
                          <SimpleGrid columns={2} gap={4}>
                            <Box>
                              <Text>Total Quantity: {quantityToSell}</Text>
                            </Box>
                            <Box textAlign="right">
                              <Text fontWeight="bold">Total Amount: {totalAmount.toFixed(2)}</Text>
                            </Box>
                          </SimpleGrid>
                        </Box>
                      </Stack>
                    )}

            {!selectedSupply?.transaction && (
                    <HStack>
                        <Field.Root>
                          <Field.Label>Quantity</Field.Label>
                          <Input
                            name="quantity_sold"

                            value={saleFormData.quantity_sold}
                            onChange={(e) => setSaleFormData(prev => ({
                              ...prev,
                              quantity_sold: parseFloat(e.target.value) || 0
                            }))}
                            placeholder="Enter quantity"
                            type="number"
                            min="0"
                            max={selectedSupply ? selectedSupply.quantity - (selectedSupply.quantity_sold || 0) : 1}
                          />
                        </Field.Root>

                        <Field.Root>
                          <Field.Label>Price per Unit </Field.Label>
                          <Input
                            name="selling_price"
                            value={saleFormData.selling_price}
                            onChange={(e) => setSaleFormData(prev => ({
                              ...prev,
                              selling_price: parseFloat(e.target.value) || 0
                            }))}
                            placeholder="Enter selling price"
                            type="number"
                            step="0.01"
                          />
                        </Field.Root>
                  </HStack>
              )}
              <Field.Root>
                <Field.Label>VAT Settings</Field.Label>
                <VStack align="stretch" gap={2}>
                  <Checkbox.Root
                     checked={includeVat}
                     onCheckedChange={(e) => setIncludeVat(!!e.checked)}
                     size="md"
                  >
                      <Checkbox.HiddenInput />
                      <Checkbox.Control>
                        <Checkbox.Indicator />
                      </Checkbox.Control>
                    <Checkbox.Label>Include VAT</Checkbox.Label>
                  </Checkbox.Root>
                  {includeVat && (
                    <Input
                      type="number"
                      value={vatRate}
                      onChange={(e) => setVatRate(Number(e.target.value))}
                      placeholder="VAT %"
                      width="100px"
                      min={0}
                      max={100}
                    />
                  )}
                </VStack>
              </Field.Root>

              <HStack>
                      <Field.Root>
                        <Field.Label>Amount Details</Field.Label>
                        <VStack align="stretch" gap={2}>
                          <HStack justify="space-between">
                            <Text>Net Amount:</Text>
                            <Text> {!selectedSupply?.transaction ?((saleFormData.quantity_sold * saleFormData.selling_price).toFixed(2))-(calculateVat(saleFormData.quantity_sold * saleFormData.selling_price).toFixed(2)):(totalAmount.toFixed(2)- calculateVat(totalAmount).toFixed(2))}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text>VAT ({vatRate}%):</Text>
                            <Text>{!selectedSupply?.transaction ?calculateVat(saleFormData.quantity_sold * saleFormData.selling_price).toFixed(2) : calculateVat(totalAmount).toFixed(2)}</Text>
                          </HStack>
                          <HStack justify="space-between" fontWeight="bold">
                            <Text>Total Amount:</Text>
                            <Text>
                              {!selectedSupply?.transaction ?((saleFormData.quantity_sold * saleFormData.selling_price).toFixed(2)):(totalAmount.toFixed(2))}
                            </Text>
                          </HStack>
                        </VStack>
                      </Field.Root>
                      <Separator orientation="vertical" height="8rem" />
                      <Field.Root>
                        <Field.Label>Attachments (LPOs, Receipts, etc.)</Field.Label>
                        <FileUploadRoot
                          maxW="xl"
                          maxFiles={5}
                          maxFileSize={5000000}
                          onFileAccept={handleFileUpload}
                          onFileReject={handleFileReject}
                          allowDrop={true}
                          accept={{
                            'image/*': ['.png', '.jpg', '.jpeg'],
                            'application/pdf': ['.pdf'],
                            'text/csv': ['.csv'],
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
                            'application/vnd.ms-excel': ['.xls'],
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
                          }}
                        >
                          <HStack>
                            <FileUploadTrigger asChild>
                              <Button variant="outline" size="sm"><FiUpload />Upload file</Button>
                            </FileUploadTrigger>
                            <Box flex="1">
                              {saleFormData?.attachments?.length > 0 ? (
                                <List.Root>
                                  {saleFormData?.attachments?.map((file, index) => (
                                    <List.Item key={index} display="flex" alignItems="center" gap={2}>
                                      <Box display="flex" alignItems="center">
                                        <Text fontSize="md">{file.name}</Text>
                                        <Icon
                                          ml={6}
                                          color="tomato"
                                          onClick={() => handleFileRemove(file)}
                                          cursor="pointer"
                                        >
                                          <FiX />
                                        </Icon>
                                      </Box>
                                    </List.Item>
                                  ))}
                                </List.Root>
                              ) : (
                                <Text>No files uploaded yet</Text>
                              )}
                            </Box>
                          </HStack>
                        </FileUploadRoot>
                      </Field.Root>
              </HStack>



            {!selectedSupply?.transaction &&(
             <>
              <Field.Root>
                <Field.Label>Buyer Type</Field.Label>
                <RadioGroup.Root
                  value={saleFormData.is_supplier? 'supplier' : 'customer'}
                  onValueChange={(value) => {
                    setSaleFormData(prev => ({
                      ...prev,
                      is_supplier: value == 'supplier',
                      customer_name: '',
                      customer_contact: '',
                      buyer_supplier_id: ''
                    }));
                  }}
                >
                  <HStack gap={4}>
                    <RadioGroup.Item value="customer">
                        <RadioGroup.ItemHiddenInput />
                        <RadioGroup.ItemIndicator />
                        <RadioGroup.ItemText>Customer</RadioGroup.ItemText>
                    </RadioGroup.Item>
                    <RadioGroup.Item value="supplier">
                        <RadioGroup.ItemHiddenInput />
                        <RadioGroup.ItemIndicator />
                        <RadioGroup.ItemText>Supplier</RadioGroup.ItemText>
                    </RadioGroup.Item>
                  </HStack>
                </RadioGroup.Root>
              </Field.Root>



                      {saleFormData.is_supplier ? (
                        <Field.Root>
                          <Field.Label>Select Buyer Supplier</Field.Label>
                          <NativeSelectRoot size="md">
                            <NativeSelectField
                              name="buyer_supplier_id"
                              value={saleFormData.buyer_supplier_id || ''}
                              onChange={(e) => setSaleFormData(prev => ({
                                ...prev,
                                buyer_supplier_id: e.target.value
                              }))}
                              bg={selectBg}
                              color={selectColor}
                              borderColor={selectBorderColor}
                              _hover={{ borderColor: selectHoverBorderColor }}
                              _focus={{
                                borderColor: selectFocusBorderColor,
                                boxShadow: selectFocusBoxShadow,
                                bg: selectBg,
                              }}
                            >
                              <option value="">Select a supplier</option>
                              {suppliers
                                .filter(s => s.supplier_id != selectedSupply?.supplier_id)
                                .map(supplier => (
                                  <option
                                    key={supplier.supplier_id}
                                    value={supplier.supplier_id}
                                  >
                                    {supplier.supplier_name}
                                  </option>
                                ))}
                            </NativeSelectField>
                          </NativeSelectRoot>
                        </Field.Root>

                      ) : (
                          <>
                            <Field.Root>
                              <Field.Label>Customer Name (Optional)</Field.Label>
                              <Input
                                name="customer_name"
                                value={saleFormData.customer_name}
                                onChange={(e) => setSaleFormData(prev => ({
                                  ...prev,
                                  customer_name: e.target.value
                                }))}
                                placeholder="Enter customer name"
                              />
                            </Field.Root>

                            <Field.Root>
                              <Field.Label>Customer Contact (Optional)</Field.Label>
                              <Input
                                name="customer_contact"
                                value={saleFormData.customer_contact}
                                onChange={(e) => setSaleFormData(prev => ({
                                  ...prev,
                                  customer_contact: e.target.value
                                }))}
                                placeholder="Enter customer contact"
                              />
                            </Field.Root>
                        </>
                 )}
                 </>
                )
                }

                </VStack>
          </DialogBody>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
                    setShowSaleForm(false);
                    setSelectedSupply(null);
                    setSelectedItems({});
                    setAcceptedFiles([]);
                    setTotalAmount(0);
                    setQuantityToSell(0);
              }}
            >Cancel</Button>
            <Button colorScheme="blue" onClick={handleSaleSubmit} disabled={isLoading} >Complete Sale</Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>


      {/* Sale Payment Modal */}
      <DialogRoot open={showSalePaymentModal} onOpenChange={(e) => setShowSalePaymentModal(e.open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Record Sale Payment</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <VStack gap={4}>
              <Text>Sale ID: {selectedSaleForPayment?.sale_id}</Text>
              <Input
                type="number"
                value={salePaymentAmount}
                onChange={(e) => setSalePaymentAmount(Number(e.target.value))}
                placeholder="Enter payment amount"
              />
              <Box w="100%">
                <Heading size="sm">Previous Payments</Heading>
                {salePaymentHistory.length > 0 ? salePaymentHistory.map(p => (
                  <HStack key={p.id} justify="space-between">
                    <Text>{new Date(p.date).toLocaleDateString()} - {formatCurrency(p.amount)}</Text>
                    <Badge>{p.status}</Badge>
                  </HStack>
                )) : <Text>No previous payments</Text>}
              </Box>
              <FileUploadRoot inputProps={{ multiple: true, onChange: e => setSalePaymentFiles(Array.from((e.target as HTMLInputElement).files || [])) }}>
                <FileUploadDropzone label="Attach Evidence" description="Upload payment proof" />
              </FileUploadRoot>
              <FileUploadList files={salePaymentFiles} showSize clearable />
            </VStack>
          </DialogBody>
          <DialogFooter>
            <Button colorScheme="blue" onClick={handleSubmitSalePayment}>Submit</Button>
            <Button variant="ghost" onClick={() => setShowSalePaymentModal(false)}>Cancel</Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>

      {/* Edit Sale Modal */}
      <DialogRoot open={showEditSaleModal} onOpenChange={(e) => setShowEditSaleModal(e.open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Sale Amounts</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <VStack gap={4}>
              <Input type="number" placeholder="Quantity Sold" value={editSaleData?.quantity_sold} onChange={e => setEditSaleData(d => d && ({...d, quantity_sold: Number(e.target.value)}))} />
              <Input type="number" placeholder="Selling Price" value={editSaleData?.selling_price} onChange={e => setEditSaleData(d => d && ({...d, selling_price: Number(e.target.value)}))} />
            </VStack>
          </DialogBody>
          <DialogFooter>
            <Button colorScheme="blue" onClick={handleSubmitEditSale} disabled={isLoading}>Save</Button>
            <Button variant="ghost" onClick={() => setShowEditSaleModal(false)}>Cancel</Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>

      {/* Attachments Modal */}
      { showAttachments && (
                <DialogRoot open={showAttachments} onOpenChange={(e) => setShowAttachments(e.open)}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Supply Attachments</DialogTitle>
                </DialogHeader>
                <DialogBody>
                  <VStack gap={4} align="stretch">
                    {selectedAttachments?.length > 0 ? (
                      <List.Root>
                        {selectedAttachments.map((attachment, index) => (
                          <List.Item
                            key={index}
                            p={3}
                            borderWidth="1px"
                            borderRadius="md"
                            display="flex"
                            justifyContent="space-between"
                            alignItems="center"
                          >
                            <HStack gap={4}>
                              <Icon boxSize={5}><FiFile/></Icon>
                              <Text>{attachment.split('/').pop()}</Text>
                            </HStack>
                            <HStack gap={2}>
                            <Button
                                size="sm"
                                onClick={async () => {
                                  setIsLoading(true);
                                  try {
                                    const url = `${baseApiUrl}/pic/${attachment}`;
                                    const response = await fetch(url);
                                    if (!response.ok) throw new Error('Failed to download file');
                                    const blob = await response.blob();
                                    const blobUrl = window.URL.createObjectURL(blob);
                                    const link = document.createElement('a');
                                    link.href = blobUrl;
                                    link.download = attachment || 'download';
                                    document.body.appendChild(link);
                                    link.click();
                                    link.remove();
                                    window.URL.revokeObjectURL(blobUrl);
                                  } catch (error) {
                                    toaster.error({
                                      title: "Failed to fetch attachment",
                                      description: error instanceof Error ? error.message : "An unexpected error occurred",
                                    });
                                  } finally {
                                    setIsLoading(false);
                                  }
                                }}
                              >
                                <FiDownload />
                                Print
                              </Button>

                            </HStack>
                          </List.Item>
                        ))}
                      </List.Root>
                    ) : (
                      <Text color="gray.500" textAlign="center">No attachments available</Text>
                    )}
                  </VStack>
                </DialogBody>
                <DialogFooter>
                  <Button onClick={() => setShowAttachments(false)}>Close</Button>
                </DialogFooter>
              </DialogContent>
            </DialogRoot>

            )}

      {/* Invoice Modal */}
      <DialogRoot open={showInvoice && !!selectedSupply} onOpenChange={(e) => setShowInvoice(e.open)}>
        <DialogContent size="lg">
          <DialogHeader>
            <DialogTitle>Supply Invoice #{selectedSupply?.supply_id}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            {selectedSupply && (
              <VStack align="stretch" gap={4}>
                {currentShop && (
                  <HStack>
                    {currentShop.shop_logo_url && (

                        <Image src={currentShop.shop_logo_url[0]} alt={currentShop.shop_name} width={200} height={200} objectFit="fill"/>
                    )}
                    <Center>
                      <VStack align="start">
                        <Text fontSize="2xl" fontWeight="bold">{currentShop.shop_name}</Text>
                        <Text fontSize="sm">{currentShop.shop_location_name}</Text>
                        {currentShop.shop_location_address && <Text fontSize="sm">{currentShop.shop_location_address}</Text>}
                      </VStack>
                    </Center>
                  </HStack>
                )}

                <HStack justifyContent="space-between">
                  <Box>
                    <Text fontWeight="bold">Supplier:</Text>
                    <Text>{suppliers.find(s => s.supplier_id == selectedSupply.supplier_id)?.supplier_name || 'Unknown'}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold">Date:</Text>
                    <Text>{new Date(selectedSupply.supply_date).toLocaleDateString()}</Text>
                  </Box>
                </HStack>

                <Box>
                  <Text fontWeight="bold" mb={2}>Details:</Text>
                  <Table.Root variant="outline" size="sm">
                    <Table.Header>
                      <Table.Row>
                        <Table.ColumnHeader>Item</Table.ColumnHeader>
                        <Table.ColumnHeader>Quantity</Table.ColumnHeader>
                        <Table.ColumnHeader>Unit Price</Table.ColumnHeader>
                        <Table.ColumnHeader>Total</Table.ColumnHeader>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      <Table.Row>
                        <Table.Cell>{selectedSupply.item_name}</Table.Cell>
                        <Table.Cell>{selectedSupply.quantity}</Table.Cell>
                        <Table.Cell>{(selectedSupply.amount_expected / selectedSupply.quantity).toFixed(2)}</Table.Cell>
                        <Table.Cell>{Number(selectedSupply.amount_expected).toFixed(2)}</Table.Cell>
                      </Table.Row>
                    </Table.Body>
                    <Table.Footer>
                      <Table.Row>
                        <Table.Cell colSpan={3} textAlign="right"><strong>Total Expected:</strong></Table.Cell>
                        <Table.Cell>{Number(selectedSupply.amount_expected).toFixed(2)}</Table.Cell>
                      </Table.Row>
                      <Table.Row>
                        <Table.Cell colSpan={3} textAlign="right"><strong>Amount Paid:</strong></Table.Cell>
                        <Table.Cell>{Number(selectedSupply.amount_paid).toFixed(2)}</Table.Cell>
                      </Table.Row>
                      <Table.Row>
                        <Table.Cell colSpan={3} textAlign="right"><strong>Balance:</strong></Table.Cell>
                        <Table.Cell>{Number(selectedSupply.amount_expected - selectedSupply.amount_paid).toFixed(2)}</Table.Cell>
                      </Table.Row>
                    </Table.Footer>
                  </Table.Root>
                </Box>

                <HStack justifyContent="flex-end">
                  <Box textAlign="right">
                    <Text><strong>Total Expected:</strong> {selectedSupply.amount_expected}</Text>
                    <Text><strong>Total Paid:</strong> {selectedSupply.amount_paid}</Text>
                    <Text fontWeight="bold"><strong>Balance:</strong> {selectedSupply.amount_expected - selectedSupply.amount_paid}</Text>
                  </Box>
                </HStack>

                {/* KRA eTim Section */}
                {/* <Box border="1px solid" borderColor="gray.200" p={4} borderRadius="md">
                  <Heading size="sm" mb={2}>KRA eTim Information</Heading>
                  <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                    <Box>
                      <Text fontWeight="bold">eTim Invoice Number:</Text>
                      <Text>ET-{Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}</Text>
                    </Box>
                    <Box>
                      <Text fontWeight="bold">Tax Amount:</Text>
                      <Text>{(selectedSupply.amount_expected * 0.16).toFixed(2)}</Text>
                    </Box>
                    <Box>
                      <Text fontWeight="bold">VAT Rate:</Text>
                      <Text>16%</Text>
                    </Box>
                    <Box>
                      <Text fontWeight="bold">Invoice Date:</Text>
                      <Text>{new Date().toLocaleDateString()}</Text>
                    </Box>
                  </Grid>
                </Box> */}

              </VStack>
            )}
          </DialogBody>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInvoice(false)}>Close</Button>
            <Button colorScheme="blue" onClick={handlePrintInvoice}>
              <FiPrinter /> Print Invoice
            </Button>
            <Button colorScheme="cyan" onClick={() => handleGenerateReport('supplies')}>
              <FiDownload />Download Invoice
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>

      {/* sales Invoice Modal */}
      <DialogRoot open={showSaleInvoice && !!selectedSale} onOpenChange={(e) => setShowSaleInvoice(e.open)}>
        <DialogContent id="sale-invoice-modal">
          <div id="invoice-content">
              <DialogHeader>
                <DialogTitle>Supply Sale Invoice #{selectedSale?.invoce_no}</DialogTitle>
              </DialogHeader>
              <DialogBody>
                {selectedSale && (
                  <VStack align="stretch" gap={2}>
                      {currentShop && (
                      <HStack gap={5}>
                        {currentShop.shop_logo_url && (
                            <Image src={currentShop.shop_logo_url[0]} alt={currentShop.shop_name} width={200} height={200} objectFit="fit"/>
                        )}
                        <Center>
                          <VStack align="start">
                            <Text fontSize="2xl" fontWeight="bold">{currentShop.shop_name}</Text>
                            <Text fontSize="sm">{currentShop.shop_location_name}</Text>
                            {currentShop.shop_location_address && <Text fontSize="sm">{currentShop.shop_location_address}</Text>}
                          </VStack>
                        </Center>
                      </HStack>
                    )}

                    <HStack justifyContent="space-between">
                      <Box gap={2}>
                        <Text fontWeight="bold">Customer: {selectedSale?.customer_name}</Text>
                        {selectedSale.supply_branch && <Text fontWeight="bold">Branch: {selectedSale.supply_branch}</Text>}
                        {selectedSale.customer_contact && (<Text fontWeight="bold">Contact: {selectedSale.customer_contact}</Text>)}
                      </Box>
                      <Box>
                        <Text fontWeight="bold">{new Date(selectedSale.sale_date).toLocaleDateString()}</Text>
                      </Box>
                    </HStack>

                    <Box>
                      <Text fontWeight="bold" mb={2}>Details:</Text>
                      <Table.Root variant="outline" size="sm">
                        <Table.Header>
                          <Table.Row>
                            <Table.ColumnHeader>Item</Table.ColumnHeader>
                            <Table.ColumnHeader>Quantity</Table.ColumnHeader>
                            <Table.ColumnHeader>Unit Price</Table.ColumnHeader>
                            <Table.ColumnHeader>Total</Table.ColumnHeader>
                          </Table.Row>
                        </Table.Header>
                        <Table.Body>
                                {selectedSale?.extra_data?.length > 0 ?(
                                    selectedSale?.extra_data.map((item, index) => (
                                      <Table.Row key={item?.receipt_item_id || index}>
                                        <Table.Cell>{item?.item_name}</Table.Cell>
                                        <Table.Cell>{item?.quantity}</Table.Cell>
                                        <Table.Cell>{item?.unit_price}</Table.Cell>
                                        <Table.Cell>{item?.total_amount}</Table.Cell>
                                      </Table.Row>
                                    ))
                                  ) : (
                                  <Table.Row>
                                    <Table.Cell>
                                      {supplyItems.find(item => item.supply_id == selectedSale.supply_id)?.item_name || 'Unknown Item'}
                                    </Table.Cell>
                                    <Table.Cell>{selectedSale.quantity_sold}</Table.Cell>
                                    <Table.Cell>{selectedSale.selling_price}</Table.Cell>
                                    <Table.Cell>{selectedSale.total_amount}</Table.Cell>
                                  </Table.Row>
                                )}
                        </Table.Body>
                        <Table.Footer>
                          <Table.Row>
                            <Table.Cell colSpan={3} textAlign="right"><strong>Subtotal:</strong></Table.Cell>
                            <Table.Cell>{selectedSale.total_amount - (selectedSale.vat_amount ? selectedSale.vat_amount : 0)}</Table.Cell>
                          </Table.Row>
                          {selectedSale.vat_amount>0 && (
                            <>
                            <Table.Row>
                              <Table.Cell colSpan={3} textAlign="right"><strong>Tax Rate:</strong></Table.Cell>
                              <Table.Cell>{selectedSale.vat_rate}%</Table.Cell>
                            </Table.Row>

                            <Table.Row>
                              <Table.Cell colSpan={3} textAlign="right"><strong>Tax Amount:</strong></Table.Cell>
                              <Table.Cell>{selectedSale.vat_amount}</Table.Cell>
                            </Table.Row>
                            </>
                            )}
                          <Table.Row>
                            <Table.Cell colSpan={3} textAlign="right"><strong>Total:</strong></Table.Cell>
                            <Table.Cell>{selectedSale.total_amount}</Table.Cell>
                          </Table.Row>
                        </Table.Footer>
                      </Table.Root>
                    </Box>



                  </VStack>
                )}
              </DialogBody>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowSaleInvoice(false)}>Close</Button>
                {/* <Button colorScheme="blue" onClick={handlePrintInvoice}>
                  <FiPrinter /> Print Invoice
                </Button> */}
                <Button colorScheme="cyan" onClick={() => handleGenerateReport('sales')}>
                  <FiDownload /> Download Invoice
                </Button>
              </DialogFooter>
          </div>
        </DialogContent>
      </DialogRoot>

      {/* Supplier Details Modal */}
      <DialogRoot open={showSupplierDetails} onOpenChange={(e) => setShowSupplierDetails(e.open)}>
        <DialogContent size="lg">
          <DialogHeader>
            <DialogTitle>Supplier Details</DialogTitle>
          </DialogHeader>
          <DialogBody>
            {selectedSupplier && (
              <VStack align="stretch" gap={4}>
                <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                  <Box>
                    <Text fontWeight="bold">Name:</Text>
                    <Text>{selectedSupplier.supplier_name}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold">Contact:</Text>
                    <Text>{selectedSupplier.supplier_contact}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold">Email:</Text>
                    <Text>{selectedSupplier.supplier_email}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold">Address:</Text>
                    <Text>{selectedSupplier.supplier_address || 'N/A'}</Text>
                  </Box>
                  <Box>
                    <Text fontWeight="bold">Status:</Text>
                    <Badge colorScheme={selectedSupplier.supplier_status == 'active' ? 'cyan' : 'red'}>
                      {selectedSupplier.supplier_status}
                    </Badge>
                  </Box>
                </Grid>

                {/* Supply History */}
                <Box>
                  <Heading size="sm" mb={2}>Supply Items</Heading>
                  <Table.Root size="sm" variant="outline">
                      <Table.Header>
                        <Table.Row>
                          <Table.ColumnHeader>Date</Table.ColumnHeader>
                          <Table.ColumnHeader>Item</Table.ColumnHeader>
                          <Table.ColumnHeader>Quantity</Table.ColumnHeader>
                          <Table.ColumnHeader>Amount</Table.ColumnHeader>
                          <Table.ColumnHeader>Status</Table.ColumnHeader>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {supplyItems
                          .filter(item => item.supplier_id == selectedSupplier.supplier_id)
                          .map((item, index) => (
                            <Table.Row key={index}>
                              <Table.Cell>{new Date(item.supply_date).toLocaleDateString()}</Table.Cell>
                              <Table.Cell>{item.item_name}</Table.Cell>
                              <Table.Cell>{item.quantity}</Table.Cell>
                              <Table.Cell>{formatCurrency(item.amount_expected)}</Table.Cell>
                              <Table.Cell>
                                <Badge colorScheme={item.amount_paid >= item.amount_expected ? 'cyan' : 'yellow'}>
                                  {item.amount_paid >= item.amount_expected ? 'Paid' : 'Pending'}
                                </Badge>
                              </Table.Cell>
                            </Table.Row>
                          ))}
                      </Table.Body>
                    </Table.Root>
                </Box>
              </VStack>
            )}
          </DialogBody>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSupplierDetails(false)}>Close</Button>
            <Button
              colorScheme="blue"
              onClick={() => {
                setShowSupplierDetails(false);
                handleEditSupplier(selectedSupplier);
              }}
            >
              Edit Supplier
            </Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>

      {/* Supply Payment Request Modal */}
      <DialogRoot open={showSupplyPaymentModal} onOpenChange={(e) => setShowSupplyPaymentModal(e.open)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Request Supply Payment</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <VStack gap={4}>
              <Text>Supply ID: {selectedSupplyForPayment?.supply_id}</Text>
              <Input
                type="number"
                value={supplyPaymentAmount}
                onChange={(e) => setSupplyPaymentAmount(Number(e.target.value))}
                placeholder="Enter payment amount"
              />
              <Box w="100%">
                <Heading size="sm">Previous Payments</Heading>
                {supplyPaymentHistory.length > 0 ? supplyPaymentHistory.map(p => (
                  <HStack key={p.id} justify="space-between">
                    <Text>{new Date(p.date).toLocaleDateString()} - {formatCurrency(p.amount)}</Text>
                    <Badge>{p.status}</Badge>
                  </HStack>
                )) : <Text>No previous payments</Text>}
              </Box>
              <FileUploadDropzone files={supplyPaymentFiles} onFilesChange={setSupplyPaymentFiles} />
            </VStack>
          </DialogBody>
          <DialogFooter>
            <Button colorScheme="blue" onClick={handleSubmitSupplyPayment}>Submit</Button>
            <Button variant="ghost" onClick={() => setShowSupplyPaymentModal(false)}>Cancel</Button>
          </DialogFooter>
        </DialogContent>
      </DialogRoot>

    </Box>
  );
};

export default SuppliersPage;
