import  React,{ useState,useEffect,useCallback} from "react";
import Table_ from '@/components/pos/Table_default1'
import {fetchData,updateData,deleteData, fetchData2} from '@/app/utils/pos';
import {Transaction} from '@/app/utils/definitions'
import { toaster } from "@/components/ui/toaster"
import {formatDate} from  '@/app/utils/actions'
import { Table ,Field,Input,Box,HStack,VStack,Text,Stack,DataList,Separator, Button,Badge,Heading,Card,List } from "@chakra-ui/react"
import {User} from '@/lib/features/users'
import { SlActionUndo,SlBasketLoaded } from "react-icons/sl";
import { NativeSelectField, NativeSelectRoot } from "@/components/ui/native-select";
import { useAppDispatch,useAppSelector } from "@/lib/hooks";
import { updateUser } from "@/lib/features/users";

const Pending = ({status,mode,user,query,checkTransaction}:{status:number|number[],mode:string,user:User;query:string,checkTransaction:any}) => {
  const [transaction, setTransaction] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [Loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const shop_id =user?.currentshop;
  const username=user?.username;
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState('');
  const [allocating, setAllocating] = useState(false);
  const dispatch = useAppDispatch();
  // const user = useAppSelector((state) => state.user.currentUser);
    

  const getUsers = async () => {
    setLoading(true);
    try {
      const queryParams = { 
        query: {
          shop_id,
          type: 'order'
        }
      };
      
      const response = await fetchData<User>('get_users', null, null, queryParams);
      
      if (response && response.data) {
        setUsers(response.data as any[]);
      } else {
        setUsers([]);
      }
    } catch (error) {
      toaster.error({
        title: 'Failed to load data',
        description: 'An unexpected error occurred: ' + error
      });
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };


  const LoadTabData = useCallback(async () => {
      setIsLoading(true);
      const queryParams = { query,shop_id: shop_id,trans_status: status,username: username};
      try {
        const TransactionResponse = await fetchData<Transaction>('transaction_',currentPage,pageSize,queryParams);
        const { data: TransactionData, metadata: TransactionMetadata } = TransactionResponse;
        setTransaction(Array.isArray(TransactionData) ? TransactionData : [TransactionData]);
        setTotalItems(TransactionMetadata.total);
      } catch (error) {
        toaster.error({
          title: 'Failed to load data.',
          description:'An unexpected error occurred.'+error,
        });
      } finally {
        setIsLoading(false);
        getUsers();
      }
  }, [currentPage, pageSize, query]);
  
      useEffect(() => {
        LoadTabData();
      
      },[LoadTabData]);
      const handlePageChange = (newPage: any) => {
        setCurrentPage(newPage.page);
      };
    
      const handlePageSizeChange = (newPageSize: any) => {
        setPageSize(newPageSize.pagesize);
        setCurrentPage(1);
      };
  
       const toggleRow = (trans_id: string) => {
          setExpandedRows((prev) => ({
            ...prev,
            [trans_id]: !prev[trans_id],
          }));
        };
      const columns = [
        { label: 'Transaction ID', key: 'trans_id', align: 'left' },
        { label: 'Type', key: 'trans_type', align: 'left' },
        { label: 'Quantiy', key: 'trans_quantity', align: 'center' },
        { label: 'Amount', key: 'trans_total', align: 'center'},
        { label: 'Status', key: 'status', align: 'left',
          render: (status) => status?.status_description || 'Undefined'
        },
        { 
          label: 'Created', 
          key: 'created_at', 
          align: 'center',
          render: (created_at) => formatDate(created_at)
        },
        { 
          label: 'Updated', 
          key: 'updated_at', 
          align: 'center',
          render: (updated_at) => formatDate(updated_at)
        },
        { label: 'Action', key: 'actions', align: 'center' }
      ];
    
      const getActions = (item) => [
        {label: 'View',colorScheme: 'blue',onClick: () => toggleRow(item.trans_id),icon: <SlBasketLoaded />, }
      ];

  const renderRowDetails = (item) => {

    const handleAllocate = async (item: any,status:any) => {
      setAllocating(true);
      try {
        const payload: any = {
          trans_status: status,
          trans_id: item.trans_id,
          username: username
        };
        
        if (status !== 0) {
          payload.order_allocation = selectedUser;
        }
        await updateData('transaction_', item.trans_id,payload);
        if (status == 0) {
          dispatch(updateUser({ ...user, transaction_id: item.trans_id }));
          toggleRow(item.trans_id);
          toaster.success({
            title: 'Order Sent to counter',
            description: 'Order has been Populated on the counter or pending'
          });
        } else {
          toaster.success({
            title: 'Order allocated to(selectedUser)',
            description: 'Order has been successfully allocated'
          });
      }
        setSelectedUser('');
        LoadTabData();
      } catch (error) {
        toaster.error({
          title: 'Allocation failed',
          description: 'Failed to allocate order: ' + error
        });
      } finally {
        setAllocating(false);
        checkTransaction('allocate',item?.trans_id);
      }
    };


  const handleReverse = async () => {
      setLoading(true);
      try {
        await updateData('transaction_', item.trans_id, {
          trans_id:item.trans_id,
          reversed: true,
          reversed_count: item.reversed_count + 1,
          trans_status: 0});
          toggleRow(item.trans_id);
          toaster.success({
            title: 'Transaction Sent to counter',
            description: 'Transaction has been Populated on the counter or pending'
          });
      } catch (error) {
        toaster.error({
          title: 'Failed to load data.',
          description:'An unexpected error occurred.'+error,
        });
      } finally {
        setLoading(false);
        LoadTabData();
        checkTransaction('allocate',item.trans_id);
      }
    };
 
return(
        <HStack gap={4} align="start">
         <VStack align="start" gap={2} flex={1}>
            <HStack><Text fontWeight="semibold">Cashier:</Text><Text>  {item.user?.first_names +'  ' +item.user?.last_names}</Text></HStack>
            <HStack><Text fontWeight="semibold">User:</Text><Text>  {item.user?.username}</Text></HStack>
              <VStack gap={2}>
                  <Heading size="sm" color="fg.muted" mb={2}>Transaction Summary</Heading>
                  <VStack gap={2}>
                    <HStack justify="space-between">
                      <Text fontWeight="medium" color="fg.muted">Taxes:</Text>
                      <Text  color="red.500">{item.trans_tax?item.trans_tax:0.00}</Text>
                    </HStack>
                    <Separator />
                    <HStack justify="space-between">
                      <Text fontWeight="medium" color="fg.muted">Net:</Text>
                      <Text>{item.trans_net?item.trans_net:0.00}</Text>
                    </HStack>
                    <Separator />
                    <HStack justify="space-between">
                      <Text fontWeight="medium" color="fg.muted">Discount:</Text>
                      <Text color="blue.500">{item.trans_discount?item.trans_discount:0.00}</Text>
                    </HStack>
                    <Separator />
                    <HStack justify="space-between">
                      <Text fontWeight="bold">Total Amount:</Text>
                      <Text fontWeight="bold">{item.trans_total?item.trans_total:0.00}</Text>
                    </HStack>
                  </VStack>
                  {item.payments && item.payments.length > 0 ? (
                    <Card.Root variant="outline" >
                      <DataList.Root orientation="vertical">
                        {item.payments?.map((payment, index) => (
                          <DataList.Item key={index} py={2} px={3}>
                            <HStack justify="space-between">
                              <DataList.ItemLabel fontWeight="medium">
                                {payment.paymentMethod?.payment_method_name || "No payment"}
                              </DataList.ItemLabel>
                              <DataList.ItemValue>
                                {payment.paymentMethod ? payment.payment_amount : "N/A"}
                              </DataList.ItemValue>
                            </HStack>
                            {payment.payment_response?.message && (
                              <Text fontSize="xs" color="fg.muted" mt={1}>
                                {payment.payment_response.message}
                              </Text>
                            )}
                          </DataList.Item>
                        ))}
                      </DataList.Root>
                    </Card.Root>
                  ) : (
                    <Card.Root variant="outline" p={4}>
                      <Text color="fg.muted" textAlign="center">No payments yet</Text>
                    </Card.Root>
                  )}
              </VStack>

              <Stack> 
                  {item.trans_status == 19 || item.trans_status == 21 ? (
                    <>
                     <Separator size="lg" />
                    <Box mt={2}>
                   
                      <HStack> <Text fontWeight="semibold" mb={1}>Allocated to:</Text> <Text color={'purple'}>{item.allocatedUser?item.allocatedUser?.first_names + '  ' + item.allocatedUser?.last_names : 'Not allocated'}</Text>
                      </HStack>
                 
                    <VStack gap={2} align="stretch">
                      <HStack>
                        <NativeSelectRoot    disabled={allocating}>
                          <NativeSelectField
                            value={selectedUser}
                            onChange={(e) => setSelectedUser(e.target.value)}
                          
                          >
                            <option value="">Select user...</option>
                            {users.map((user) => (
                              <option key={user.user_id} value={user.user_id}>
                                {user.first_names} {user.last_names}
                              </option>
                            ))}
                          </NativeSelectField>
                        </NativeSelectRoot>
                        <Button 
                          size="sm" 
                          colorPalette="blue" 
                          onClick={() => handleAllocate(item,21)} 
                          disabled={!selectedUser || allocating}
                        >
                          {allocating ? 'Allocating...' : 'Allocate'}
                        </Button>
                      </HStack>
                      <Button 
                          size="sm" 
                          colorPalette="purple" 
                          onClick={() => handleAllocate(item,0)}
                        >
                          {allocating ? 'Completing...' : 'Complete'}
                        </Button>
                    </VStack>
                   </Box>
                  </>
                ):(
                  <>
                  {/* <Badge colorPalette="red">After reversal ... Hold the current Transaction</Badge> */}
                  <Button onClick={handleReverse} disabled={Loading} colorPalette={'purple'}>
                    <SlActionUndo />
                    {Loading ? 'Reversing...' : 'Reverse'}
                  </Button>
                  </>
                )}
                </Stack>

            
            </VStack>
    
            <VStack align="start" gap={2} flex={1}>
                <Text fontWeight="bold">Items bought</Text>
                <Box 
                maxH="10rem" 
                overflowY="auto" 
                w="100%" 
                borderRadius="xl" 
                boxShadow="lg"
                >
                  <Table.Root size="sm" stickyHeader>
                    <Table.Header>
                      <Table.Row>
                        <Table.ColumnHeader>Item</Table.ColumnHeader>
                        <Table.ColumnHeader>Quantity</Table.ColumnHeader>
                        <Table.ColumnHeader>Total</Table.ColumnHeader>
                      </Table.Row>
                    </Table.Header>
                      <Table.Body>
                        {item.receipts.map((receipt) => (
                          <Table.Row key={receipt.receipt_id}>
                            <Table.Cell>{receipt.receipt_item}</Table.Cell>
                            <Table.Cell>{receipt.receipt_quantity}</Table.Cell>
                            <Table.Cell>{receipt.receipt_total}</Table.Cell>
                          </Table.Row>
                        ))}
                      </Table.Body>
                  </Table.Root>
                </Box>
              </VStack>
        </HStack>
        )};

      
  return (
          <Table_
          columns={columns}
          data={transaction.map((item) => ({ ...item, isExpanded: expandedRows[item?.trans_id] || false }))}
          currentPage={currentPage}
          totalItems={totalItems}
          pageSize={pageSize}
          handlePageChange={handlePageChange}
          handlePageSizeChange={handlePageSizeChange}
          getActions={getActions}
          isLoading={isLoading}
          width="100%"
          height="100%"
          mode={mode}
          renderRowDetails={renderRowDetails}
        />
  );
};

export default Pending;
