'use client'
import  React,{ useState,useEffect,useCallback} from "react";
import {Box,Tabs,Card,Heading,HStack, Button,Stack,VStack,SimpleGrid,Menu} from '@chakra-ui/react';
import jsPDF from 'jspdf';
import Table from '@/components/pos/Table_default2'
import Search from '@/components/pos/search'


import { StatHelpText,StatRoot,StatValueText, } from "@/components/ui/stat"
import {DialogBody,DialogCloseTrigger,DialogContent,DialogRoot,DialogTrigger,} from "@/components/ui/dialog"
import {Product} from '@/app/utils/definitions'
import { FiDownload,FiPlus,FiFolderPlus,FiBriefcase,FiArrowDown} from "react-icons/fi";
import { z } from "zod";
import { toaster } from "@/components/ui/toaster"
import {fetchData} from '@/app/utils/pos';
import { ItemForm,Add_items,Add_category,Add_discounts,ProductView,Add_payment} from "@/components/pos/invetory_functions";
import {useAppSelector } from "@/lib/hooks";
import Filter from '@/components/pos/filters'

const Inventory = () => {

  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;
  const shopName = user?.shops?.find(s => s.shop_id === shop_id)?.shop_name || 'Store';

  const [products, setProducts] = useState<Product[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [query, setQuery] = useState('');
  const [totalItems, setTotalItems] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [onAdd, setAdd] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [AddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const [totalProducts, settotalProducts] = useState(0);
  const [topSellingProducts, settopSellingProducts] = useState([]);
  const [lowStockItems, setlowStockItems] = useState([]);
  // const [isEditModalOpen, setIsEditModalOpen] = useState(0);


  const loadData = useCallback(async () => {
    setIsLoading(true);
    const queryParams = { query, shop_id };
    try {
      const productResponse = await fetchData<Product>('items', currentPage, pageSize, queryParams);
      const { data: productData, metadata: productMetadata } = productResponse;
      setProducts(productData);
      setTotalItems(productMetadata.total);
    } catch (error) {
      toaster.error({
        title: 'Failed to load data.',
        description: error instanceof z.ZodError ? error.errors[0]?.message : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, pageSize, query, onAdd]);

  useEffect(() => {
    loadData();
    handleFetch();
  }, [loadData]);

  const handlePageChange = (newPage: any) => {
    setCurrentPage(newPage.page);
  };

  const handlePageSizeChange = (newPageSize: any) => {
    setPageSize(newPageSize.pagesize);
    setCurrentPage(1);
  };

  const handleSearch = (searchValue: string) => {
    setQuery(searchValue);
    setCurrentPage(1);
  };

  const handleItemAdded = () => {
    setAdd(prev => !prev);
  };

  const stats = [
    {
      title: "Total Products",
      color: "orange.500",
      values: [
        { value: totalProducts, helpText: "current sales" },
      ],
    },
    {
      title: "Top Selling",
      color: "purple.500",
      values: [
        {
          value:topSellingProducts[0]?.item_name,
          helpText: topSellingProducts?.length > 0  ? topSellingProducts[0]?.receipt_total: 0
        },
      ],
      isCurrency: false,
      isText: true

    },
    {
      title: "Low Stock",
      color: "red.500",
      values: [
        { 
          value: lowStockItems.length > 0 ?lowStockItems[0].item_name:0, 
          helpText: lowStockItems.length > 0 ?lowStockItems[0].item_quantity:0
        },
      ],
      isCurrency: false,
      isText: true
    },
  ];

  const handleFetch = async () => {
    if (!shop_id) return;
    try {
        const response = await fetchData('cal_invet', null, null, { shop_id });
        if (response) { 
          settotalProducts(response?.totalProducts); 
          settopSellingProducts(response?.topSellingProducts);
          setlowStockItems(response?.lowStockItems)}
    } catch (error) {
        toaster.error({title: "Error on server",description: 'An unexpected error occurred.', });
    }
  };

  const exportInventory = async (format: 'csv' | 'pdf' | 'tags') => {
    console.log('Export clicked:', format);
    try {
      const response = await fetchData('items', 1, 10000, { 
        shop_id, 
        query, 
        export: true 
      });
      console.log('Export response:', response);
      if (response?.data) {
        if (format === 'csv') {
          downloadCSV(response.data);
        } else if (format === 'tags') {
          downloadPriceTags(response.data);
        } else {
          downloadPDF(response.data);
        }
        toaster.success({ title: `${format.toUpperCase()} exported successfully` });
      } else {
        throw new Error('No data received');
      }
    } catch (error) {
      console.error('Export error:', error);
      toaster.error({ title: 'Export failed', description: error?.message || 'Unknown error' });
    }
  };

  const downloadCSV = (data) => {
    console.log('Downloading CSV with data:', data);
    if (!data || data.length === 0) {
      toaster.error({ title: 'No data to export' });
      return;
    }
    
    const headers = ['ID', 'Name', 'Selling Price', 'Buying Price', 'Quantity', 'Brand', 'Model', 'Sub Category', 'Category', 'Discount Name', 'Discount Amount', 'Status', 'Description', 'Created Date', 'Updated Date'];
    const csvContent = [
      headers.join(','),
      ...data.map(item => [
        item.item_id || '',
        `"${item.item_name || ''}"`,
        item.item_selling || 0,
        item.item_buying || 0,
        item.item_quantity || 0,
        `"${item.item_brand || ''}"`,
        `"${item.item_model_no || ''}"`,
        `"${item.item_sub_cat || ''}"`,
        `"${item.Category?.category_name || ''}"`,
        `"${item.Discount?.discount_name || ''}"`,
        item.Discount?.discount_amount || 0,
        `"${item.status?.status_name || ''}"`,
        `"${(item.item_description || '').replace(/\n/g, ' ').substring(0, 100)}"`,
        new Date(item.created_at).toLocaleDateString(),
        new Date(item.updated_at).toLocaleDateString()
      ].join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `inventory_comprehensive_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log('CSV download triggered');
  };

  const downloadPDF = (data) => {
    console.log('Downloading PDF with data:', data);
    if (!data || data.length === 0) {
      toaster.error({ title: 'No data to export' });
      return;
    }
    
    const pdf = new jsPDF();
    const pageHeight = pdf.internal.pageSize.height;
    let yPosition = 20;
    
    // Header
    pdf.setFillColor(41, 128, 185);
    pdf.rect(0, 0, 210, 30, 'F');
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(18);
    pdf.setFont('helvetica', 'bold');
    pdf.text('INVENTORY REPORT', 20, 20);
    
    yPosition = 40;
    pdf.setTextColor(0, 0, 0);
    
    // Summary
    const totalValue = data.reduce((sum, item) => sum + (parseFloat(item.item_selling) * item.item_quantity), 0);
    const totalCost = data.reduce((sum, item) => sum + (parseFloat(item.item_buying) * item.item_quantity), 0);
    
    pdf.setFontSize(10);
    pdf.text(`Generated: ${new Date().toLocaleDateString()} | Items: ${data.length} | Value: ${totalValue.toFixed(2)} | Profit: ${(totalValue - totalCost).toFixed(2)}`, 20, yPosition);
    yPosition += 15;
    
    // Items
    data.forEach((item, index) => {
      if (yPosition > pageHeight - 40) {
        pdf.addPage();
        yPosition = 20;
      }
      
      // Item header
      pdf.setFillColor(240, 240, 240);
      pdf.rect(15, yPosition - 3, 180, 8, 'F');
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${index + 1}. ${item.item_name}`, 20, yPosition);
      yPosition += 10;
      
      // Item details
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`ID: ${item.item_id} | Brand: ${item.item_brand || 'N/A'} | Model: ${item.item_model_no || 'N/A'}`, 25, yPosition);
      yPosition += 5;
      pdf.text(`Category: ${item.Category?.category_name || 'N/A'} | Sub: ${item.item_sub_cat || 'N/A'}`, 25, yPosition);
      yPosition += 5;
      pdf.text(`Selling: ${item.item_selling} | Buying: ${item.item_buying} | Qty: ${item.item_quantity}`, 25, yPosition);
      yPosition += 5;
      pdf.text(`Discount: ${item.Discount?.discount_name || 'None'} (${item.Discount?.discount_amount || 0}) | Status: ${item.status?.status_name}`, 25, yPosition);
      yPosition += 8;
    });
    
    pdf.save(`inventory_${new Date().toISOString().split('T')[0]}.pdf`);
    console.log('PDF download triggered');
  };

  const downloadPriceTags = (data) => {
    console.log('Downloading price tags with data:', data);
    if (!data || data.length === 0) {
      toaster.error({ title: 'No data to export' });
      return;
    }
    
    const pdf = new jsPDF();
    const tagWidth = 60;
    const tagHeight = 40;
    const margin = 10;
    const cols = 3;
    const rows = 7;
    let currentItem = 0;
    
    // Color palette: pink, white, black accents
    const colors = [
      [255, 182, 193], // Light pink
      [255, 105, 180], // Hot pink
      [0, 0, 0],       // Black
      [255, 255, 255], // White
      [255, 20, 147]   // Deep pink
    ];
    
    data.forEach((item, index) => {
      const col = currentItem % cols;
      const row = Math.floor((currentItem % (cols * rows)) / cols);
      
      if (currentItem > 0 && currentItem % (cols * rows) === 0) {
        pdf.addPage();
      }
      
      const x = margin + col * (tagWidth + 5);
      const y = margin + row * (tagHeight + 5);
      
      // Random color selection
      const colorIndex = index % colors.length;
      const bgColor = colors[colorIndex];
      const isLight = bgColor[0] > 200 || bgColor[1] > 200 || bgColor[2] > 200;
      
      // Clean white background
      pdf.setFillColor(255, 255, 255);
      pdf.rect(x, y, tagWidth, tagHeight, 'F');
      
      // Colored top stripe
      pdf.setFillColor(...bgColor);
      pdf.rect(x, y, tagWidth, 6, 'F');
      
      // Shop name in stripe
      pdf.setTextColor(isLight ? 0 : 255, isLight ? 0 : 255, isLight ? 0 : 255);
      pdf.setFontSize(6);
      pdf.setFont('helvetica', 'bold');
      pdf.text(shopName.substring(0, 12), x + 2, y + 4);
      
      // Item name
      pdf.setTextColor(0, 0, 0);
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'bold');
      pdf.text((item.item_name || '').substring(0, 15), x + 2, y + 12);
      
      // Price - prominent
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(0, 0, 0);
      pdf.text(`${item.item_selling}`, x + 2, y + 22);
      
      // Brand/Size
      pdf.setTextColor(0, 0, 0);
      pdf.setFontSize(6);
      pdf.text(`${item.item_brand || ''} ${item.item_model_no || ''}`.trim().substring(0, 15), x + 2, y + 28);
      
      // Simple border
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.3);
      pdf.rect(x, y, tagWidth, tagHeight);
      
      // Discount corner
      if (item.Discount?.discount_amount > 0) {
        pdf.setFillColor(255, 20, 147);
        pdf.triangle(x + tagWidth - 8, y, x + tagWidth, y, x + tagWidth, y + 8, 'F');
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(4);
        pdf.text(`-${item.Discount.discount_amount}`, x + tagWidth - 6, y + 4);
      }
      
      currentItem++;
    });
    
    pdf.save(`${shopName}_tags_${new Date().toISOString().split('T')[0]}.pdf`);
    console.log('Price tags PDF download triggered');
  };


  const columns = [
    { label: 'Id', key: 'item_id', align: 'left' },
    { label: 'Name', key: 'item_name', align: 'left' },
    { label: 'Selling price', key: 'item_selling', align: 'left' },
    { label: 'Buying price', key: 'item_buying', align: 'left' },
    { label: 'Quantity', key: 'item_quantity', align: 'left' },
    { label: 'Discount', key: 'Discount.discount_amount', align: 'left' },
    { label: 'Model', key: 'item_model_no', align: 'left' },
    { label: 'Availability', key: 'status.status_name', align: 'left' },
    { label: 'Action', key: 'actions', align: 'center' }
  ];

  const toggleModal = (item: Product) => {
    if (!isEditModalOpen) {
      setSelectedProduct(item);
    } else { 
      setSelectedProduct(null);}
    setIsEditModalOpen((prev) => !prev);
  };
  const handleView = (item: Product) => {
    setSelectedProduct(item);
    setIsViewModalOpen(true);
  };
  const getActions = (item: Product) => [
    { key: `update-${item.item_id}`, label: 'Update', colorScheme: 'blue', onClick: () =>toggleModal(item)},
    { key: `view-${item.item_id}`, label: 'View', colorScheme: 'green', onClick: () =>handleView(item)},
  ];

      return (
 
    <Stack direction="column" gap={6} p={{ base: 3, md: 6 }}>
        {/* Stats and Search/Actions Section */}
        <Stack direction={{ base: "column", lg: "row" }} gap={6} w="100%">
          {/* Stats Section - Now full width on mobile */}
          <Box flex={{ base: "1", lg: "2" }} minW="0">
            <SimpleGrid columns={{ base: 1, sm: 2, lg: 3 }} gap={4}>
              {stats.map((stat, index) => (
                <Card.Root 
                  key={index} 
                  p={4} 
                  borderRadius="lg" 
                  boxShadow="sm"
                  transition="all 0.2s"
                  _hover={{ boxShadow: "md", transform: "translateY(-2px)" }}
                >
                  <Stack gap={3}>
                    <Heading color={stat.color} size="sm" fontWeight="semibold">
                      {stat.title}
                    </Heading>
                    <StatRoot>
                      <HStack gap={6} flexWrap="wrap">
                        {stat.values.map((item, idx) => (
                          <VStack key={idx} align="start" gap={2}>
                             <StatValueText textStyle="md" whiteSpace="nowrap" display="inline">{item.value}</StatValueText>
                            <StatHelpText fontSize="md" whiteSpace="nowrap" display="inline">{item.helpText}</StatHelpText>
                         
                          </VStack>

                        ))}
                      </HStack>
                    </StatRoot>
                  </Stack>
                </Card.Root>
              ))}
            </SimpleGrid>
          </Box>

          {/* Search and Actions Section */}
          <Box flex={{ base: "1", lg: "1" }} minW="0">
            <VStack gap={4} w="100%">
              {/* Search Box - Full width on mobile */}
              <Box w="100%">
                <Search
                  placeholder="Search for items"
                  tableName="inventory"
                  onSearch={handleSearch}
                />
              </Box>

              {/* Action Buttons - Responsive layout */}
              <Box w="100%">
                <Stack direction={{ base: "column", sm: "row" }} gap={4}>
                  <DialogRoot 
                    open={AddModalOpen} 
                    onOpenChange={(e) => setIsAddModalOpen(e.open)}
                    closeOnEscape={true}
                    placement='top'
                    size="xl"
                    motionPreset="slide-in-top"
                    lazyMount={true}
                    preventScroll
                  >
                    <DialogTrigger asChild>
                      <Button 
                        colorScheme="blue" 
                        flexShrink={0}
                        size={'xs'}
                        w={{ base: "100%", sm: "auto" }}
                      ><FiPlus />
                        Add Items
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogBody>
                        <Tabs.Root defaultValue="members">
                          <Tabs.List>
                            <Tabs.Trigger value="members"><FiPlus /> Add Item</Tabs.Trigger>
                            <Tabs.Trigger value="projects"><FiFolderPlus /> Add Items(Bulk)</Tabs.Trigger>
                            <Tabs.Trigger value="payment"><FiBriefcase /> Add Payment Methods</Tabs.Trigger>
                            <Tabs.Trigger value="category"><FiBriefcase /> Add Categories</Tabs.Trigger>
                            <Tabs.Trigger value="discounts"><FiArrowDown /> Add Discounts</Tabs.Trigger>
                          </Tabs.List>
                          <Tabs.Content value="members">
                            <ItemForm mode="insert" item={null} onSubmit={handleItemAdded} onClose={() => setIsAddModalOpen(false)} />
                          </Tabs.Content>
                          <Tabs.Content value="projects">
                            <Add_items />
                          </Tabs.Content>
                          <Tabs.Content value="category">
                            <Add_category mode={'insert'} id={null} />
                          </Tabs.Content>
                          <Tabs.Content value="payment">
                            <Add_payment/>
                          </Tabs.Content>
                          <Tabs.Content value="discounts">
                            <Add_discounts mode={'insert'} id={null} />
                          </Tabs.Content>
                        </Tabs.Root>
                      </DialogBody>
                      <DialogCloseTrigger />
                    </DialogContent>
                  </DialogRoot>

                  <Filter setQuery={setQuery} shop_id={shop_id} />
                  
                  <Button 
                    colorScheme="blue" 
                    flexShrink={0}
                    size={'xs'}
                    w={{ base: "100%", sm: "auto" }}
                    onClick={() => exportInventory('csv')}
                  ><FiDownload />
                    CSV
                  </Button>
                  <Button 
                    colorScheme="teal" 
                    flexShrink={0}
                    size={'xs'}
                    w={{ base: "100%", sm: "auto" }}
                    onClick={() => exportInventory('pdf')}
                  ><FiDownload />
                    PDF
                  </Button>
                  <Button 
                    colorScheme="purple" 
                    flexShrink={0}
                    size={'xs'}
                    w={{ base: "100%", sm: "auto" }}
                    onClick={() => exportInventory('tags')}
                  ><FiDownload />
                    Tags
                  </Button>
                </Stack>
              </Box>
            </VStack>
          </Box>
        </Stack>

        {/* Table Section */}
        <Card.Root 
          boxShadow="xl" 
          w="100%" 
          p={{ base: 2, md: 4 }}
          transition="all 0.2s"
          _hover={{ boxShadow: "2xl" }}
        >
          <Table
            columns={columns}
            data={products}
            currentPage={currentPage}
            totalItems={totalItems}
            pageSize={pageSize}
            handlePageChange={handlePageChange}
            handlePageSizeChange={handlePageSizeChange}
            getActions={getActions}
            isLoading={isLoading}
            width="100%"
            height="100%"
          />
        </Card.Root>

        {/* Modals */}
        {isViewModalOpen && (
          <DialogRoot 
            open={isViewModalOpen} 
            onOpenChange={(e) => setIsViewModalOpen(e.open)}
            closeOnEscape={true}
            placement='top'
            size="xl"
            motionPreset="slide-in-top"
            lazyMount={true}
            preventScroll
          >    
            <DialogContent>
              <DialogBody>
                <ProductView item={selectedProduct} onClose={() => setIsViewModalOpen(false)} />
              </DialogBody>
              <DialogCloseTrigger />
            </DialogContent>
          </DialogRoot>
        )}
        
        {isEditModalOpen && selectedProduct && (
          <DialogRoot 
            open={isEditModalOpen} 
            onOpenChange={(e) => setIsEditModalOpen(e.open)}
            closeOnEscape={true}
            placement='top'
            size="xl"
            motionPreset="slide-in-top"
            lazyMount={true}
            preventScroll
          >    
            <DialogContent>
              <DialogBody>
                <ItemForm
                  mode="update"
                  item={selectedProduct}
                  onSubmit={handleItemAdded}
                  onClose={() => setIsEditModalOpen(false)}
                />
              </DialogBody>
              <DialogCloseTrigger />
            </DialogContent>
          </DialogRoot>
        )}
      </Stack>
    );
};

export default Inventory;



