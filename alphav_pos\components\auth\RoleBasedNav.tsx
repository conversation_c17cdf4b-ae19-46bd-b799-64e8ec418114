'use client';

import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { getAllowedRoutes, hasRoutePermission } from '@/app/utils/rbac';
import { Box, VStack, HStack, Text, Button, Badge } from '@chakra-ui/react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  LuShoppingCart, 
  LuPackage, 
  LuChartBar, 
  LuUsers, 
  LuTruck,
  LuSettings
} from 'react-icons/lu';

interface NavItem {
  path: string;
  label: string;
  icon: React.ReactNode;
  description?: string;
}

const NAV_ITEMS: NavItem[] = [
  {
    path: '/pos/sale',
    label: 'Sales',
    icon: <LuShoppingCart />,
    description: 'Process sales and transactions'
  },
  {
    path: '/pos/inventory',
    label: 'Inventory',
    icon: <LuPackage />,
    description: 'Manage stock and inventory'
  },
  {
    path: '/pos/stock',
    label: 'Supplies',
    icon: <LuTruck />,
    description: 'Manage suppliers and stock'
  },
  {
    path: '/pos/reports',
    label: 'Reports',
    icon: <LuChartBar/>,
    description: 'View analytics and reports'
  },
  {
    path: '/pos/manager',
    label: 'Management',
    icon: <LuSettings />,
    description: 'Admin and management tools'
  }
];

interface RoleBasedNavProps {
  variant?: 'sidebar' | 'horizontal' | 'grid';
  showDescriptions?: boolean;
}

export default function RoleBasedNav({ 
  variant = 'sidebar', 
  showDescriptions = false 
}: RoleBasedNavProps) {
  const router = useRouter();
  const pathname = usePathname();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  if (!currentUser || !currentUser.roles) {
    return null;
  }

  const allowedRoutes = getAllowedRoutes(currentUser.roles);
  const allowedNavItems = NAV_ITEMS.filter(item => 
    allowedRoutes.some(route => item.path.startsWith(route))
  );

  const handleNavigation = (path: string) => {
    if (hasRoutePermission(currentUser.roles, path)) {
      router.push(path);
    }
  };

  const isActive = (path: string) => {
    return pathname.startsWith(path);
  };

  const getUserRoleBadge = () => {
    // Handle both string roles and role objects
    const primaryRoleData = currentUser.roles[0];
    const primaryRole = typeof primaryRoleData === 'string'
      ? primaryRoleData
      : primaryRoleData?.role_name || 'user';

    const roleColors: Record<string, string> = {
      admin: 'red',
      manager: 'purple',
      supervisor: 'blue',
      pos: 'green',
      inventory: 'orange',
      supplies: 'teal',
      reports: 'cyan'
    };

    return (
      <Badge
        colorScheme={roleColors[primaryRole] || 'gray'}
        variant="solid"
        textTransform="capitalize"
      >
        {primaryRole}
      </Badge>
    );
  };

  if (variant === 'grid') {
    return (
      <Box p={4}>
        <HStack justify="space-between" mb={6}>
          <Text fontSize="lg" fontWeight="bold">Dashboard</Text>
          {getUserRoleBadge()}
        </HStack>
        <Box 
          display="grid" 
          gridTemplateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }}
          gap={4}
        >
          {allowedNavItems.map((item) => (
            <Box
              key={item.path}
              p={6}
              borderWidth={1}
              borderRadius="lg"
              cursor="pointer"
              bg={isActive(item.path) ? "blue.50" : "white"}
              borderColor={isActive(item.path) ? "blue.200" : "gray.200"}
              _hover={{ bg: "gray.50", borderColor: "blue.300" }}
              onClick={() => handleNavigation(item.path)}
              transition="all 0.2s"
            >
              <VStack gap={3} align="start">
                <HStack>
                  <Box fontSize="2xl" color="blue.500">
                    {item.icon}
                  </Box>
                  <Text fontWeight="bold" fontSize="lg">
                    {item.label}
                  </Text>
                </HStack>
                {showDescriptions && (
                  <Text fontSize="sm" color="gray.600">
                    {item.description}
                  </Text>
                )}
              </VStack>
            </Box>
          ))}
        </Box>
      </Box>
    );
  }

  if (variant === 'horizontal') {
    return (
      <HStack gap={2} p={4} bg="white" borderBottomWidth={1}>
        <Text fontSize="sm" color="gray.600" mr={4}>
          Welcome, {currentUser.username}
        </Text>
        {getUserRoleBadge()}
        <Box flex={1} />
        {allowedNavItems.map((item) => (
          <Button
            key={item.path}
            variant={isActive(item.path) ? "solid" : "ghost"}
            colorScheme={isActive(item.path) ? "blue" : "gray"}
            size="sm"
            onClick={() => handleNavigation(item.path)}
          >
            <HStack gap={2}>
              <Box>{item.icon}</Box>
              <Text>{item.label}</Text>
            </HStack>
          </Button>
        ))}
      </HStack>
    );
  }

  // Default sidebar variant
  return (
    <VStack gap={2} align="stretch" p={4} bg="gray.50" minH="100vh" w="250px">
      <Box mb={4}>
        <Text fontSize="sm" color="gray.600">
          Welcome, {currentUser.username}
        </Text>
        {getUserRoleBadge()}
      </Box>
      
      {allowedNavItems.map((item) => (
        <Button
          key={item.path}
          variant={isActive(item.path) ? "solid" : "ghost"}
          colorScheme={isActive(item.path) ? "blue" : "gray"}
          justifyContent="flex-start"
          onClick={() => handleNavigation(item.path)}
          size="md"
        >
          <HStack gap={2}>
            <Box>{item.icon}</Box>
            <Text>{item.label}</Text>
          </HStack>
        </Button>
      ))}
    </VStack>
  );
}
