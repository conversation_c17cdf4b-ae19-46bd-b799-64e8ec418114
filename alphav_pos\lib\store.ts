import { configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from 'redux-persist/lib/storage';
import userReducer from "@/lib/features/users";
import photoReducer from "@/lib/features/photo";
import { thunk } from "redux-thunk";

const persistConfig = {key: "root",storage,};
const persistedUserReducer = persistReducer(persistConfig, userReducer);

// Create store instance
let storeInstance: ReturnType<typeof configureStore> | null = null;
let persistorInstance: ReturnType<typeof persistStore> | null = null;

export const makeStore = () => {
  if (!storeInstance) {
    storeInstance = configureStore({
      reducer: {
        user: persistedUserReducer,
        photo: photoReducer,
      },
      devTools: process.env.NODE_ENV != "production",
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({serializableCheck: false}),
    });
    persistorInstance = persistStore(storeInstance);
  }
  return { store: storeInstance, persistor: persistorInstance };
};

// Export persistor for global access
export const getPersistor = () => {
  if (!persistorInstance) {
    makeStore();
  }
  return persistorInstance!;
};

export type AppStore = ReturnType<typeof makeStore>["store"];
export type RootState = ReturnType<AppStore["getState"]>;
export type AppDispatch = AppStore["dispatch"];

