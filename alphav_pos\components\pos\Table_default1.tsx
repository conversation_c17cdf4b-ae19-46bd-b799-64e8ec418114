import { Table,Box, HStack,Button,Flex,Stack,Center,Spinner,IconButton, Text} from '@chakra-ui/react';
import { PaginationItems, PaginationNextTrigger, PaginationPageText, PaginationPrevTrigger, PaginationRoot, } from "@/components/ui/pagination"
import { NativeSelectRoot, NativeSelectField } from "@/components/ui/native-select"
import { LuShoppingBag,LuUsers } from "react-icons/lu"
import { EmptyState } from "@/components/ui/empty-state"
import { Collapsible } from "@chakra-ui/react"
import react ,{Fragment } from 'react'
const Table_ = ({
  columns,
  data,
  currentPage,
  totalItems,
  pageSize,
  handlePageChange,
  handlePageSizeChange,
  getActions,
  isLoading,
  width,
  height,
  mode,
  renderRowDetails
}) => {


  const totalPages = Math.ceil(totalItems / pageSize);
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));

  return (
    <Stack direction='column' h={height} w={width}  borderRadius="xl" boxShadow="lg" p={2}>
      <Table.Root stickyHeader size={'sm'}>
        <Table.Header>
          <Table.Row bg="bg.subtle">
            {columns.map((col, index) => (
              <Table.ColumnHeader key={index} textAlign={col.align || 'left'}>
                {col.label}
              </Table.ColumnHeader>
            ))}
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {isLoading ? (
            <Table.Row>
              <Table.Cell colSpan={columns.length} textAlign="center">
                <Center h="200px">
                  <Spinner size="xl" color="red.600"/>
                </Center>
              </Table.Cell>
            </Table.Row>
          ) : data.length == 0 ? (
            <Table.Row>
              <Table.Cell colSpan={columns.length} textAlign="center">
                <Center h="69vh">
                  <EmptyState
                    icon={ mode == 'Users' ? <LuUsers /> :<LuShoppingBag />}
                    // title={"Pending/Hold"}
                    title={mode}
                    description={ mode == 'Users' ?`${mode} list is empty`:`No ${mode} Transaction`}
                  />
                </Center>
              </Table.Cell>
            </Table.Row>
          ) :(
            <>
              {data.map((item, index) => (
                <Fragment  key={index}>
                    <Table.Row >
                      {columns.map((col, colIndex) => (
                        <Table.Cell key={colIndex} textAlign={col.align || 'left'}>
                          {col.key === 'actions' ? (
                            <HStack gap={2} justify="center">
                              {getActions(item).map((action, actionIndex) => (
                                action.icon ? ( // Check if the action has an icon
                                  <IconButton
                                    key={actionIndex}
                                    size="xs"
                                    colorPalette={action.colorScheme || undefined}
                                    onClick={() => action.onClick(item)}
                                    aria-label={action.label}
                                    p={3}
                                  >
                                    {action.icon}   {action.label}
                                  </IconButton>
                                ) : (
                                  <Button
                                    key={actionIndex}
                                    size="xs"
                                    colorPalette={action.colorScheme || undefined}
                                    onClick={() => action.onClick(item)}
                                  >
                                    {action.label}
                                  </Button>
                                )
                              ))}
                            </HStack>
                          ) : (
                            col.render ? col.render(item[col.key]) : (
                              col.key.includes('.')
                                ? col.key.split('.').reduce((acc, part) => acc?.[part], item)
                                : item[col.key]
                            )
                            // col.key.includes('.') ? (col.key.split('.').reduce((acc, part) => acc?.[part], item)) : (item[col.key])

                          )}
                        </Table.Cell>
                      ))}
                    </Table.Row>
                  {
                    item.isExpanded && (
                      <Table.Row>
                        <Table.Cell colSpan={columns.length}>
                          <Collapsible.Root open={item.isExpanded}>
                            <Collapsible.Content>
                              <Box p={4} borderRadius="md">
                                {renderRowDetails(item)}
                              </Box>
                            </Collapsible.Content>
                          </Collapsible.Root>
                        </Table.Cell>
                      </Table.Row>
                    )
                  }
              </Fragment>
              ))}
            </>
          )}
        </Table.Body>
      </Table.Root>
      <PaginationRoot
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        page={validCurrentPage}
        count={totalItems}
        pageSize={pageSize}
        defaultPage={1}
        size='sm'
      >
        <HStack justify="space-between" align="center" w="full">
          <HStack gap={2}>
            <PaginationPageText />
            <HStack gap={2} align="center">
              <Text fontSize="sm" color="gray.600">Show:</Text>
              <NativeSelectRoot size="sm" w="80px">
                <NativeSelectField
                  value={pageSize}
                  onChange={(e) => handlePageSizeChange({ pagesize: parseInt(e.target.value) })}
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </NativeSelectField>
              </NativeSelectRoot>
              <Text fontSize="sm" color="gray.600">per page</Text>
            </HStack>
          </HStack>
          <Flex justify="center" align="center" gap={2}>
            <PaginationPrevTrigger />
            <PaginationItems />
            <PaginationNextTrigger />
          </Flex>
        </HStack>
      </PaginationRoot>
    </Stack>
  );
};
export default Table_;
