import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Photo {
  id: string;
  url: string;
}

interface PhotoState {
  photos: Photo[];
}

const initialState: PhotoState = {
  photos: [],
};

const photoSlice = createSlice({
  name: 'photo',
  initialState,
  reducers: {
    setPhotos: (state, action: PayloadAction<Photo[]>) => {
      state.photos = action.payload;
    },
    addPhoto: (state, action: PayloadAction<Photo>) => {
      state.photos.push(action.payload);
    },
    removePhoto: (state, action: PayloadAction<string>) => {
      state.photos = state.photos.filter((photo) => photo.id !== action.payload);
    },
  },
});

export const { setPhotos, addPhoto, removePhoto } = photoSlice.actions;
export default photoSlice.reducer;
