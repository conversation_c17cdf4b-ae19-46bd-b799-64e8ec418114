{"name": "--alphav2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^3.2.3", "@hookform/resolvers": "^4.0.0", "@next/third-parties": "^15.1.3", "@react-google-maps/api": "^2.20.5", "@reduxjs/toolkit": "^2.5.1", "@uploadthing/react": "^7.3.1", "@vercel/postgres": "^0.10.0", "axios": "^1.7.9", "fs": "0.0.1-security", "gsap": "^3.12.7", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.469.0", "moment": "^2.30.1", "motion": "^11.15.0", "next": "15.1.3", "next-redux-wrapper": "^8.1.0", "next-themes": "^0.4.4", "pg": "^8.13.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-slick": "^0.30.3", "recharts": "^2.15.0", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "slick-carousel": "^1.8.1", "uploadthing": "^7.7.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^20.17.10", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "typescript": "^5.7.2"}, "packageManager": "pnpm@10.6.4+sha512.da3d715bfd22a9a105e6e8088cfc7826699332ded60c423b14ec613a185f1602206702ff0fe4c438cb15c979081ce4cb02568e364b15174503a63c7a8e2a5f6c"}